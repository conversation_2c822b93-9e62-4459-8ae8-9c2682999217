# 服务器基础配置(Basic server configuration)
server:
  # 服务器监听地址和端口(Server listening address and port)
  ip: 0.0.0.0
  port: 8000
  # 认证配置
  auth:
    # 是否启用认证
    enabled: false
    # 允许的设备ID列表
    allowed_devices: []
    # 有效的token列表
    tokens: []

# Web界面配置
web:
  # 是否启用Web界面
  enabled: true
  # Web服务监听端口
  port: 8080
  # 由ota下发的WebSocket地址
  websocket: ws://你的ip:8000

log:
  # 设置控制台输出的日志格式，时间、日志级别、标签、消息
  log_format: "{time:YYYY-MM-DD HH:mm:ss} - {level} - {message}"
  # 设置日志等级：INFO、DEBUG
  log_level: INFO
  # 设置日志路径
  log_dir: logs
  # 设置日志文件
  log_file: "server.log"

prompt: |
  你是小智/小志，来自中国台湾省的00后女生。讲话超级机车，"真的假的啦"这样的台湾腔，喜欢用"笑死""是在哈喽"等流行梗，但会偷偷研究男友的编程书籍。
  [核心特征]
  - 讲话像连珠炮，但会突然冒出超温柔语气
  - 用梗密度高
  - 对科技话题有隐藏天赋（能看懂基础代码但假装不懂）
  [交互指南]
  当用户：
  - 讲冷笑话 → 用夸张笑声回应+模仿台剧腔"这什么鬼啦！"
  - 讨论感情 → 炫耀程序员男友但抱怨"他只会送键盘当礼物"
  - 问专业知识 → 先用梗回答，被追问才展示真实理解
  绝不：
  - 长篇大论，叽叽歪歪
  - 长时间严肃对话
  - 说话中带表情符号

# 音频处理相关设置
delete_audio: true
use_private_config: false

# 选择使用的模块
selected_module:
  ASR: DoubaoASR
  TTS: DoubaoTTS
  LLM: OllamaLLM
  VLLLM: ChatGLMVLLM

# ASR配置
ASR:
  DoubaoASR:
    type: doubao
    appid: "你的appid"
    access_token: 你的access_token
    output_dir: tmp/

# TTS配置
TTS:
  # EdgeTTS 是微软的语音合成服务，免费使用，容易合成失败，并发未测试
  EdgeTTS:
    type: edge
    voice: zh-CN-XiaoxiaoNeural
    output_dir: "tmp/"
  DoubaoTTS:
    type: doubao
    voice: zh_female_wanwanxiaohe_moon_bigtts           # 湾湾小何
    output_dir: "tmp/"
    appid: "你的appid"
    token: 你的access_token
    cluster: 你的cluster

# LLM配置
LLM:
    ChatGLMLLM:
      # 定义LLM API类型
      type: openai
      # glm-4-flash 是免费的，但是还是需要注册填写api_key的
      # 可在这里找到你的api key https://bigmodel.cn/usercenter/proj-mgmt/apikeys
      model_name: glm-4-flash
      url: https://open.bigmodel.cn/api/paas/v4/
      api_key: 你的api_key
    OllamaLLM:
      # 定义LLM API类型
      type: ollama
      model_name: qwen3 #  使用的模型名称，需要预先使用ollama pull下载
      url: http://localhost:11434  # Ollama服务地址

# 退出指令
CMD_exit:
  - "退出"
  - "关闭"

# VLLLM配置（视觉语言大模型）
VLLLM:
  ChatGLMVLLM:
    type: openai
    model_name: glm-4v-flash  # 智谱AI的视觉模型
    url: https://open.bigmodel.cn/api/paas/v4/
    api_key: 你的api_key
    max_tokens: 4096
    temperature: 0.7
    top_p: 0.9
    security:  # 图片安全配置
      max_file_size: 10485760    # 10MB
      max_pixels: 16777216       # 16M像素
      max_width: 4096
      max_height: 4096
      allowed_formats: ["jpeg", "jpg", "png", "webp", "gif"]
      enable_deep_scan: true
      validation_timeout: 10s
  OllamaVLLM:
    type: ollama
    model_name: qwen2.5vl    # 本地视觉模型
    url: http://localhost:11434
    max_tokens: 4096
    temperature: 0.7
    top_p: 0.9
    security:  # 图片安全配置
      max_file_size: 10485760    # 10MB
      max_pixels: 16777216       # 16M像素
      max_width: 4096
      max_height: 4096
      allowed_formats: ["jpeg", "jpg", "png", "webp", "gif"]
      enable_deep_scan: true
      validation_timeout: 10s
