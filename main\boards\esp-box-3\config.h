#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>

#define AUDIO_INPUT_SAMPLE_RATE  24000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

#define AUDIO_INPUT_REFERENCE    true

#define AUDIO_I2S_GPIO_MCLK GPIO_NUM_2
#define AUDIO_I2S_GPIO_WS GPIO_NUM_45
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_17
#define AUDIO_I2S_GPIO_DIN  GPIO_NUM_16
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_15

#define AUDIO_CODEC_PA_PIN       GPIO_NUM_46
#define AUDIO_CODEC_I2C_SDA_PIN  GPIO_NUM_8
#define AUDIO_CODEC_I2C_SCL_PIN  GPIO_NUM_18
#define AUDIO_CODEC_ES8311_ADDR  ES8311_CODEC_DEFAULT_ADDR
#define AUDIO_CODEC_ES7210_ADDR  ES7210_CODEC_DEFAULT_ADDR

#define BUILTIN_LED_GPIO        GPIO_NUM_NC
#define BOOT_BUTTON_GPIO        GPIO_NUM_0
#define VOLUME_UP_BUTTON_GPIO   GPIO_NUM_NC
#define VOLUME_DOWN_BUTTON_GPIO GPIO_NUM_NC

#define DISPLAY_WIDTH   320
#define DISPLAY_HEIGHT  240
#define DISPLAY_MIRROR_X true
#define DISPLAY_MIRROR_Y false
#define DISPLAY_SWAP_XY false

#define DISPLAY_OFFSET_X  0
#define DISPLAY_OFFSET_Y  0

#define DISPLAY_BACKLIGHT_PIN GPIO_NUM_47
#define DISPLAY_BACKLIGHT_OUTPUT_INVERT false

/**************************************************************************************************
 * ESP32-S3-BOX pinout
 **************************************************************************************************/

/* I2C */
#define BSP_I2C_SCL (GPIO_NUM_18)
#define BSP_I2C_SDA (GPIO_NUM_8)

/* Audio */
#define BSP_I2S_SCLK (GPIO_NUM_17)
#define BSP_I2S_LCLK (GPIO_NUM_45)
#define BSP_I2S_DIN (GPIO_NUM_15)

/* Display */
#define BSP_LCD_SPI_MOSI (GPIO_NUM_6)
#define BSP_LCD_SPI_CLK (GPIO_NUM_7)
#define BSP_LCD_SPI_CS (GPIO_NUM_5)
#define BSP_LCD_DC (GPIO_NUM_4)
#define BSP_LCD_RST (GPIO_NUM_48)           // 复位电平与eye相反
#define BSP_LCD_BACKLIGHT (GPIO_NUM_47)     // 电平反的

/* Display */
#define BSP_CAMERA_XCLK (GPIO_NUM_39)
#define BSP_CAMERA_PCLK (GPIO_NUM_14)
#define BSP_CAMERA_VSYNC (GPIO_NUM_42)
#define BSP_CAMERA_HSYNC (GPIO_NUM_41)
#define BSP_CAMERA_D0 (GPIO_NUM_12)
#define BSP_CAMERA_D1 (GPIO_NUM_10)
#define BSP_CAMERA_D2 (GPIO_NUM_9)
#define BSP_CAMERA_D3 (GPIO_NUM_11)
#define BSP_CAMERA_D4 (GPIO_NUM_13)
#define BSP_CAMERA_D5 (GPIO_NUM_21)
#define BSP_CAMERA_D6 (GPIO_NUM_38)
#define BSP_CAMERA_D7 (GPIO_NUM_40)

/* uSD card */
#define BSP_SD_D0 (GPIO_NUM_44)
#define BSP_SD_CMD (GPIO_NUM_0)
#define BSP_SD_CLK (GPIO_NUM_43)

/* Buttons */
#define BSP_BUTTON_BOOT_IO (GPIO_NUM_NC)
#define BSP_BUTTONS_IO (GPIO_NUM_NC) // All 4 buttons mapped to this GPIO

typedef enum bsp_led_t {
    BSP_LED_GREEN = GPIO_NUM_46,
} bsp_led_t;





/********************    摄像头 ↑   *************************/
/***********************************************************/


#endif // _BOARD_CONFIG_H_
