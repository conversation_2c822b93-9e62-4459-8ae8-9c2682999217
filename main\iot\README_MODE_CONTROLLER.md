# 模式控制器 (ModeController) 使用说明

## 概述

ModeController 是一个新的IoT设备，用于控制ESP32客户端在不同工作模式之间切换。支持三种模式：
- **对话模式** (Chat Mode) - 默认模式，进行语音对话
- **识图模式** (Image Recognition Mode) - 自动或手动捕获图片并进行识别，结果通过TTS播放
- **指路模式** (Navigation Mode) - 用于导航功能，自动捕获路况信息

## 功能特性

### 设备属性
- `current_mode`: 当前工作模式 (0=对话模式, 1=识图模式, 2=指路模式)
- `image_capture_enabled`: 是否启用自动图片捕获
- `image_capture_interval`: 图片捕获间隔(毫秒)

### 设备方法

#### 1. 切换到对话模式
```json
{
  "method": "switch_to_chat_mode",
  "parameters": {}
}
```

#### 2. 切换到识图模式
```json
{
  "method": "switch_to_image_mode",
  "parameters": {
    "auto_capture": true,                    // 可选：是否启用自动捕获
    "capture_interval": 5000,               // 可选：捕获间隔(毫秒)
    "question": "请描述这张图片"              // 可选：询问的问题
  }
}
```

#### 3. 切换到指路模式
```json
{
  "method": "switch_to_navigation_mode",
  "parameters": {
    "auto_capture": true,      // 可选：是否启用自动捕获
    "capture_interval": 3000   // 可选：捕获间隔(毫秒)
  }
}
```

#### 4. 手动捕获图片
```json
{
  "method": "capture_image",
  "parameters": {
    "question": "请描述这张图片"  // 可选：询问的问题
  }
}
```

#### 5. 设置图片捕获
```json
{
  "method": "set_image_capture",
  "parameters": {
    "enabled": true,     // 必需：是否启用
    "interval": 3000     // 可选：间隔时间(毫秒)
  }
}
```

## 工作原理

### 图片识别流程

1. **图片捕获**: 使用现有的`Camera::Capture()`方法捕获图片
2. **图片识别**: 调用现有的`Camera::Explain(question)`方法，该方法会：
   - 将图片发送到配置的视觉识别服务端
   - 返回识别结果文本
3. **结果播放**: 将识别结果通过现有的通信协议发送给服务端进行TTS播放
4. **显示结果**: 同时在设备显示屏上显示识别结果

### 循环模式

在识图模式或指路模式下，如果启用了自动捕获：
- 系统会按设定的间隔自动捕获图片
- 每次捕获后会自动进行识别并播放结果
- 等待当前播放完成后，再进行下一次捕获
- 实现了"不断循环上传数据让服务端播报"的需求

## 通信协议

### 文本消息发送格式

识别结果通过以下格式发送到服务端：

```json
{
  "session_id": "设备会话ID",
  "type": "listen",
  "state": "detect",
  "text": "图片识别结果文本"
}
```

这个格式与现有的文本消息格式完全一致，服务端会将其作为普通文本消息处理并进行TTS播放。

## 服务端处理

### Python服务端 (xiaozhi-server)

服务端无需特殊修改，识别结果会作为普通文本消息处理：

```python
elif msg_json["state"] == "detect":
    # 处理文本消息（包括图片识别结果）
    original_text = msg_json["text"]
    # ... 现有的文本处理逻辑
    # 服务端会自动进行TTS播放
```

### 视觉识别服务配置

需要确保ESP32设备的摄像头已正确配置视觉识别服务：

```cpp
// 在设备初始化时设置视觉识别服务URL和token
camera->SetExplainUrl("http://your-vision-service/api/explain", "your-token");
```

## 使用示例

### 1. 通过IoT命令切换模式

服务端可以通过IoT命令控制设备模式：

```python
# 切换到识图模式并启用自动捕获
await invoke_iot_method(conn, "ModeController", "switch_to_image_mode", {
    "auto_capture": True,
    "capture_interval": 5000,
    "question": "请详细描述这张图片中的内容"
})

# 切换到导航模式
await invoke_iot_method(conn, "ModeController", "switch_to_navigation_mode", {
    "auto_capture": True,
    "capture_interval": 3000
})

# 手动捕获一张图片
await invoke_iot_method(conn, "ModeController", "capture_image", {
    "question": "分析这张图片中的安全隐患"
})

# 切换回对话模式
await invoke_iot_method(conn, "ModeController", "switch_to_chat_mode", {})
```

### 2. 语音控制示例

用户可以通过语音命令控制模式切换：

- "小智，切换到识图模式"
- "小智，拍一张照片"
- "小智，开始导航模式"
- "小智，回到对话模式"

### 3. 典型使用场景

#### 识图模式
```python
# 启用识图模式，每5秒自动拍照识别
await invoke_iot_method(conn, "ModeController", "switch_to_image_mode", {
    "auto_capture": True,
    "capture_interval": 5000,
    "question": "请描述你看到的内容"
})
```

#### 导航模式
```python
# 启用导航模式，每3秒自动分析路况
await invoke_iot_method(conn, "ModeController", "switch_to_navigation_mode", {
    "auto_capture": True,
    "capture_interval": 3000
})
```

## 注意事项

1. **摄像头支持**: 只有配备摄像头的开发板才能使用图片捕获功能
2. **视觉服务**: 需要配置有效的视觉识别服务URL和认证token
3. **网络连接**: 图片识别需要稳定的网络连接到视觉服务
4. **播放间隔**: 建议设置合理的捕获间隔，避免过于频繁的识别请求
5. **内存管理**: 系统会自动管理图片内存，无需手动释放

## 开发板支持

目前支持ModeController的开发板包括：
- lichuang-dev (已配置)
- 其他配备摄像头的开发板需要在对应的board文件中添加ModeController设备

要在其他开发板上启用此功能，请在board的IoT初始化部分添加：

```cpp
#if CONFIG_IOT_PROTOCOL_XIAOZHI
auto& thing_manager = iot::ThingManager::GetInstance();
thing_manager.AddThing(iot::CreateThing("ModeController"));
#endif
```

## 技术实现

### 核心优势

1. **复用现有代码**: 直接使用现有的`Camera::Capture()`和`Camera::Explain()`方法
2. **无需协议修改**: 通过现有的文本消息协议发送识别结果
3. **自动TTS播放**: 服务端会自动将识别结果进行TTS播放
4. **最小化修改**: 对现有代码的修改最小，降低了引入bug的风险

### 定时器实现

使用ESP32的`esp_timer`实现自动捕获功能：
- 高精度定时器，支持微秒级精度
- 自动内存管理，无内存泄漏风险
- 线程安全的回调机制
