#include "iot/thing.h"
#include "application.h"
#include "board.h"
#include "display/display.h"

#include <esp_log.h>
#include <esp_timer.h>

#define TAG "ModeController"

namespace iot {

// 设备工作模式枚举
enum WorkMode {
    kWorkModeChat = 0,      // 对话模式（默认）
    kWorkModeImageRecognition = 1,  // 识图模式
    kWorkModeNavigation = 2         // 指路模式
};

// 模式控制器IoT设备类
class ModeController : public Thing {
private:
    WorkMode current_mode_ = kWorkModeChat;
    bool image_capture_enabled_ = false;
    bool waiting_for_tts_complete_ = false; // 是否正在等待TTS播放完成
    std::string current_question_ = "请描述这张图片";

public:
    ModeController() : Thing("ModeController", "Device mode controller for switching between chat, image recognition and navigation modes") {
        // 定义设备的属性
        properties_.AddNumberProperty("current_mode", "Current work mode: 0=Chat, 1=ImageRecognition, 2=Navigation", [this]() -> int {
            return static_cast<int>(current_mode_);
        });

        properties_.AddBooleanProperty("image_capture_enabled", "Whether continuous image capture is enabled", [this]() -> bool {
            return image_capture_enabled_;
        });

        properties_.AddBooleanProperty("waiting_for_tts", "Whether waiting for TTS playback to complete", [this]() -> bool {
            return waiting_for_tts_complete_;
        });

        // 定义设备可以被远程执行的指令
        methods_.AddMethod("switch_to_chat_mode", "Switch to chat mode", ParameterList(), [this](const ParameterList& parameters) {
            SwitchMode(kWorkModeChat);
        });

        methods_.AddMethod("switch_to_image_mode", "Switch to image recognition mode", ParameterList({
            Parameter("auto_capture", "Enable continuous image capture", kValueTypeBoolean, false),
            Parameter("question", "Question to ask about the image", kValueTypeString, false)
        }), [this](const ParameterList& parameters) {
            // 设置图片捕获参数
            if (parameters.find("auto_capture") != parameters.end()) {
                image_capture_enabled_ = parameters.at("auto_capture").boolean();
            }
            if (parameters.find("question") != parameters.end()) {
                current_question_ = parameters.at("question").string();
            }
            SwitchMode(kWorkModeImageRecognition);
        });

        methods_.AddMethod("switch_to_navigation_mode", "Switch to navigation mode", ParameterList({
            Parameter("auto_capture", "Enable continuous image capture", kValueTypeBoolean, false)
        }), [this](const ParameterList& parameters) {
            // 设置图片捕获参数
            if (parameters.find("auto_capture") != parameters.end()) {
                image_capture_enabled_ = parameters.at("auto_capture").boolean();
            }
            current_question_ = "请描述前方的路况和导航信息";
            SwitchMode(kWorkModeNavigation);
        });

        methods_.AddMethod("capture_image", "Manually capture and send an image", ParameterList({
            Parameter("question", "Question to ask about the image", kValueTypeString, false)
        }), [this](const ParameterList& parameters) {
            std::string question = current_question_;
            if (parameters.find("question") != parameters.end()) {
                question = parameters.at("question").string();
            }
            CaptureAndExplainImage(question);
        });

        methods_.AddMethod("set_image_capture", "Enable or disable continuous image capture", ParameterList({
            Parameter("enabled", "Enable continuous image capture", kValueTypeBoolean, true)
        }), [this](const ParameterList& parameters) {
            image_capture_enabled_ = parameters.at("enabled").boolean();
            ESP_LOGI(TAG, "Continuous image capture %s",
                     image_capture_enabled_ ? "enabled" : "disabled");

            if (current_mode_ != kWorkModeChat && image_capture_enabled_ && !waiting_for_tts_complete_) {
                // 如果启用连续捕获且当前没有在等待TTS，立即开始第一次捕获
                TriggerNextCapture();
            }
        });

        // 内部方法：接收TTS完成通知
        methods_.AddMethod("_notify_tts_complete", "Internal method to notify TTS completion", ParameterList(), [this](const ParameterList& parameters) {
            OnTTSComplete();
        });

        // 注册TTS状态监听
        RegisterTTSListener();
    }

    ~ModeController() {
        // 析构时清理
    }

private:
    void SwitchMode(WorkMode new_mode) {
        if (current_mode_ == new_mode) {
            ESP_LOGI(TAG, "Already in mode %d", static_cast<int>(new_mode));
            return;
        }

        WorkMode old_mode = current_mode_;
        current_mode_ = new_mode;
        waiting_for_tts_complete_ = false; // 重置等待状态

        ESP_LOGI(TAG, "Switching from mode %d to mode %d", static_cast<int>(old_mode), static_cast<int>(new_mode));

        // 通知应用程序模式变化
        auto& app = Application::GetInstance();
        app.Schedule([this, new_mode]() {
            OnModeChanged(new_mode);
        });
    }

    void OnModeChanged(WorkMode new_mode) {
        auto& board = Board::GetInstance();
        auto display = board.GetDisplay();

        switch (new_mode) {
            case kWorkModeChat:
                display->SetChatMessage("system", "已切换到对话模式");
                ESP_LOGI(TAG, "Switched to Chat Mode");
                break;
            case kWorkModeImageRecognition:
                display->SetChatMessage("system", "已切换到识图模式");
                ESP_LOGI(TAG, "Switched to Image Recognition Mode");
                if (image_capture_enabled_) {
                    TriggerNextCapture();
                }
                break;
            case kWorkModeNavigation:
                display->SetChatMessage("system", "已切换到指路模式");
                ESP_LOGI(TAG, "Switched to Navigation Mode");
                if (image_capture_enabled_) {
                    TriggerNextCapture();
                }
                break;
        }
    }

    void RegisterTTSListener() {
        // 注册到Application的TTS状态监听
        auto& app = Application::GetInstance();
        // 这里需要Application提供注册TTS监听的接口
        // 暂时先在CaptureAndExplainImage中设置等待状态
    }

    void TriggerNextCapture() {
        if (current_mode_ == kWorkModeChat || !image_capture_enabled_ || waiting_for_tts_complete_) {
            return;
        }

        ESP_LOGI(TAG, "Triggering next image capture");
        auto& app = Application::GetInstance();
        app.Schedule([this]() {
            CaptureAndExplainImage(current_question_);
        });
    }

    void OnTTSComplete() {
        ESP_LOGI(TAG, "TTS playback completed, triggering next capture");
        waiting_for_tts_complete_ = false;

        // 如果当前在识图或导航模式且启用了连续捕获，触发下一次捕获
        if ((current_mode_ == kWorkModeImageRecognition || current_mode_ == kWorkModeNavigation)
            && image_capture_enabled_) {
            TriggerNextCapture();
        }
    }

    void CaptureAndExplainImage(const std::string& question) {
        ESP_LOGI(TAG, "Capturing and explaining image with question: %s", question.c_str());

        auto& board = Board::GetInstance();
        auto camera = board.GetCamera();

        if (camera == nullptr) {
            ESP_LOGW(TAG, "No camera available on this board");
            return;
        }

        // 先捕获图片
        if (!camera->Capture()) {
            ESP_LOGE(TAG, "Failed to capture image");
            return;
        }

        // 使用现有的Explain方法获取图片识别结果
        // 这会将图片发送到服务端的豆包视觉API进行处理
        std::string result = camera->Explain(question);

        ESP_LOGI(TAG, "Image explanation result: %s", result.c_str());

        // 如果启用了连续捕获，设置等待TTS完成状态
        if (image_capture_enabled_ && (current_mode_ == kWorkModeImageRecognition || current_mode_ == kWorkModeNavigation)) {
            waiting_for_tts_complete_ = true;
            ESP_LOGI(TAG, "Continuous capture enabled, waiting for TTS complete");
        }

        // 将结果通过现有通信协议发送给服务端进行TTS播放
        auto& app = Application::GetInstance();
        app.SendTextMessage(result);

        // 同时在显示屏上显示结果
        auto display = board.GetDisplay();
        if (display) {
            display->SetChatMessage("ai", result.c_str());
        }
    }

public:
    // 提供给Application调用的TTS完成通知方法
    void NotifyTTSComplete() {
        OnTTSComplete();
    }
};

} // namespace iot

DECLARE_THING(ModeController);
