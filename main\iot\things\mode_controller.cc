#include "iot/thing.h"
#include "application.h"
#include "board.h"

#include <esp_log.h>
#include <esp_timer.h>

#define TAG "ModeController"

namespace iot {

// 设备工作模式枚举
enum WorkMode {
    kWorkModeChat = 0,      // 对话模式（默认）
    kWorkModeImageRecognition = 1,  // 识图模式
    kWorkModeNavigation = 2         // 指路模式
};

// 模式控制器IoT设备类
class ModeController : public Thing {
private:
    WorkMode current_mode_ = kWorkModeChat;
    bool image_capture_enabled_ = false;
    int image_capture_interval_ = 5000; // 默认5秒间隔
    esp_timer_handle_t capture_timer_ = nullptr;
    std::string current_question_ = "请描述这张图片";

public:
    ModeController() : Thing("ModeController", "Device mode controller for switching between chat, image recognition and navigation modes") {
        // 定义设备的属性
        properties_.AddNumberProperty("current_mode", "Current work mode: 0=Chat, 1=ImageRecognition, 2=Navigation", [this]() -> int {
            return static_cast<int>(current_mode_);
        });

        properties_.AddBooleanProperty("image_capture_enabled", "Whether image capture is enabled in image recognition mode", [this]() -> bool {
            return image_capture_enabled_;
        });

        properties_.AddNumberProperty("image_capture_interval", "Image capture interval in milliseconds", [this]() -> int {
            return image_capture_interval_;
        });

        // 定义设备可以被远程执行的指令
        methods_.AddMethod("switch_to_chat_mode", "Switch to chat mode", ParameterList(), [this](const ParameterList& parameters) {
            SwitchMode(kWorkModeChat);
        });

        methods_.AddMethod("switch_to_image_mode", "Switch to image recognition mode", ParameterList({
            Parameter("auto_capture", "Enable automatic image capture", kValueTypeBoolean, false),
            Parameter("capture_interval", "Image capture interval in milliseconds", kValueTypeNumber, false),
            Parameter("question", "Question to ask about the image", kValueTypeString, false)
        }), [this](const ParameterList& parameters) {
            // 设置图片捕获参数
            if (parameters.find("auto_capture") != parameters.end()) {
                image_capture_enabled_ = parameters.at("auto_capture").boolean();
            }
            if (parameters.find("capture_interval") != parameters.end()) {
                image_capture_interval_ = static_cast<int>(parameters.at("capture_interval").number());
                if (image_capture_interval_ < 1000) {
                    image_capture_interval_ = 1000; // 最小1秒间隔
                }
            }
            if (parameters.find("question") != parameters.end()) {
                current_question_ = parameters.at("question").string();
            }
            SwitchMode(kWorkModeImageRecognition);
        });

        methods_.AddMethod("switch_to_navigation_mode", "Switch to navigation mode", ParameterList({
            Parameter("auto_capture", "Enable automatic image capture", kValueTypeBoolean, false),
            Parameter("capture_interval", "Image capture interval in milliseconds", kValueTypeNumber, false)
        }), [this](const ParameterList& parameters) {
            // 设置图片捕获参数
            if (parameters.find("auto_capture") != parameters.end()) {
                image_capture_enabled_ = parameters.at("auto_capture").boolean();
            }
            if (parameters.find("capture_interval") != parameters.end()) {
                image_capture_interval_ = static_cast<int>(parameters.at("capture_interval").number());
                if (image_capture_interval_ < 1000) {
                    image_capture_interval_ = 1000; // 最小1秒间隔
                }
            }
            current_question_ = "请描述前方的路况和导航信息";
            SwitchMode(kWorkModeNavigation);
        });

        methods_.AddMethod("capture_image", "Manually capture and send an image", ParameterList({
            Parameter("question", "Question to ask about the image", kValueTypeString, false)
        }), [this](const ParameterList& parameters) {
            std::string question = current_question_;
            if (parameters.find("question") != parameters.end()) {
                question = parameters.at("question").string();
            }
            CaptureAndExplainImage(question);
        });

        methods_.AddMethod("set_image_capture", "Enable or disable automatic image capture", ParameterList({
            Parameter("enabled", "Enable automatic image capture", kValueTypeBoolean, true),
            Parameter("interval", "Capture interval in milliseconds", kValueTypeNumber, false)
        }), [this](const ParameterList& parameters) {
            image_capture_enabled_ = parameters.at("enabled").boolean();
            if (parameters.find("interval") != parameters.end()) {
                image_capture_interval_ = static_cast<int>(parameters.at("interval").number());
                if (image_capture_interval_ < 1000) {
                    image_capture_interval_ = 1000; // 最小1秒间隔
                }
            }
            ESP_LOGI(TAG, "Image capture %s, interval: %d ms",
                     image_capture_enabled_ ? "enabled" : "disabled",
                     image_capture_interval_);

            if (current_mode_ != kWorkModeChat) {
                if (image_capture_enabled_) {
                    StartImageCapture();
                } else {
                    StopImageCapture();
                }
            }
        });

        // 创建定时器
        esp_timer_create_args_t timer_args = {
            .callback = [](void* arg) {
                ModeController* controller = static_cast<ModeController*>(arg);
                controller->OnCaptureTimer();
            },
            .arg = this,
            .dispatch_method = ESP_TIMER_TASK,
            .name = "image_capture_timer",
            .skip_unhandled_events = true
        };
        esp_timer_create(&timer_args, &capture_timer_);
    }

    ~ModeController() {
        StopImageCapture();
        if (capture_timer_) {
            esp_timer_delete(capture_timer_);
        }
    }

private:
    void SwitchMode(WorkMode new_mode) {
        if (current_mode_ == new_mode) {
            ESP_LOGI(TAG, "Already in mode %d", static_cast<int>(new_mode));
            return;
        }

        WorkMode old_mode = current_mode_;
        current_mode_ = new_mode;

        ESP_LOGI(TAG, "Switching from mode %d to mode %d", static_cast<int>(old_mode), static_cast<int>(new_mode));

        // 停止之前的自动捕获
        StopImageCapture();

        // 通知应用程序模式变化
        auto& app = Application::GetInstance();
        app.Schedule([this, new_mode]() {
            OnModeChanged(new_mode);
        });
    }

    void OnModeChanged(WorkMode new_mode) {
        auto& board = Board::GetInstance();
        auto display = board.GetDisplay();

        switch (new_mode) {
            case kWorkModeChat:
                display->SetChatMessage("system", "已切换到对话模式");
                ESP_LOGI(TAG, "Switched to Chat Mode");
                break;
            case kWorkModeImageRecognition:
                display->SetChatMessage("system", "已切换到识图模式");
                ESP_LOGI(TAG, "Switched to Image Recognition Mode");
                if (image_capture_enabled_) {
                    StartImageCapture();
                }
                break;
            case kWorkModeNavigation:
                display->SetChatMessage("system", "已切换到指路模式");
                ESP_LOGI(TAG, "Switched to Navigation Mode");
                if (image_capture_enabled_) {
                    StartImageCapture();
                }
                break;
        }
    }

    void StartImageCapture() {
        if (capture_timer_ && !esp_timer_is_active(capture_timer_)) {
            ESP_LOGI(TAG, "Starting automatic image capture with interval %d ms", image_capture_interval_);
            esp_timer_start_periodic(capture_timer_, image_capture_interval_ * 1000); // 转换为微秒
        }
    }

    void StopImageCapture() {
        if (capture_timer_ && esp_timer_is_active(capture_timer_)) {
            ESP_LOGI(TAG, "Stopping automatic image capture");
            esp_timer_stop(capture_timer_);
        }
    }

    void OnCaptureTimer() {
        // 在定时器回调中调度到主线程执行
        auto& app = Application::GetInstance();
        app.Schedule([this]() {
            CaptureAndExplainImage(current_question_);
        });
    }

    void CaptureAndExplainImage(const std::string& question) {
        ESP_LOGI(TAG, "Capturing and explaining image with question: %s", question.c_str());

        auto& board = Board::GetInstance();
        auto camera = board.GetCamera();

        if (camera == nullptr) {
            ESP_LOGW(TAG, "No camera available on this board");
            return;
        }

        // 先捕获图片
        if (!camera->Capture()) {
            ESP_LOGE(TAG, "Failed to capture image");
            return;
        }

        // 使用现有的Explain方法获取图片识别结果
        std::string result = camera->Explain(question);

        ESP_LOGI(TAG, "Image explanation result: %s", result.c_str());

        // 将结果通过现有通信协议发送给服务端进行TTS播放
        auto& app = Application::GetInstance();
        app.SendTextMessage(result);

        // 同时在显示屏上显示结果
        auto display = board.GetDisplay();
        if (display) {
            display->SetChatMessage("ai", result.c_str());
        }
    }
};

} // namespace iot

DECLARE_THING(ModeController);
