#include "mode_controller.h"
#include "application.h"
#include "board.h"

#include <esp_log.h>
#include <esp_timer.h>

#define TAG "ModeController"

namespace iot {

ModeController::ModeController() : Thing("ModeController", "Device mode controller for switching between chat, image recognition and navigation modes") {

    // 定义设备的属性
    properties_.AddNumberProperty("current_mode", "Current work mode: 0=Chat, 1=ImageRecognition, 2=Navigation", [this]() -> int {
        return static_cast<int>(current_mode_);
    });

        properties_.AddBooleanProperty("image_capture_enabled", "Whether continuous image capture is enabled", [this]() -> bool {
            return image_capture_enabled_;
        });

        properties_.AddBooleanProperty("waiting_for_tts", "Whether waiting for TTS playback to complete", [this]() -> bool {
            return waiting_for_tts_complete_;
        });

        // 定义设备可以被远程执行的指令
        methods_.AddMethod("switch_to_chat_mode", "Switch to chat mode", ParameterList(), [this](const ParameterList& parameters) {
            SwitchMode(kWorkModeChat);
        });

        methods_.AddMethod("switch_to_image_mode", "Switch to image recognition mode", ParameterList({
            Parameter("auto_capture", "Enable continuous image capture", kValueTypeBoolean, false),
            Parameter("question", "Question to ask about the image", kValueTypeString, false)
        }), [this](const ParameterList& parameters) {
            // 设置图片捕获参数
            try {
                image_capture_enabled_ = parameters["auto_capture"].boolean();
            } catch (const std::runtime_error&) {
                // 参数不存在，使用默认值
                image_capture_enabled_ = true; // 默认启用自动捕获
            }
            try {
                current_question_ = parameters["question"].string();
            } catch (const std::runtime_error&) {
                // 参数不存在，使用默认值
            }
            SwitchMode(kWorkModeImageRecognition);

            // 立即触发第一次拍照，参考MCP的实现
            auto& app = Application::GetInstance();
            app.Schedule([this]() {
                CaptureAndExplainImage(current_question_);
            });
        });

        methods_.AddMethod("switch_to_navigation_mode", "Switch to navigation mode", ParameterList({
            Parameter("auto_capture", "Enable continuous image capture", kValueTypeBoolean, false)
        }), [this](const ParameterList& parameters) {
            // 设置图片捕获参数
            try {
                image_capture_enabled_ = parameters["auto_capture"].boolean();
            } catch (const std::runtime_error&) {
                // 参数不存在，使用默认值
                image_capture_enabled_ = true; // 默认启用自动捕获
            }
            current_question_ = "请描述前方的路况和导航信息";
            SwitchMode(kWorkModeNavigation);

            // 立即触发第一次拍照，参考MCP的实现
            auto& app = Application::GetInstance();
            app.Schedule([this]() {
                CaptureAndExplainImage(current_question_);
            });
        });

        methods_.AddMethod("capture_image", "Manually capture and send an image", ParameterList({
            Parameter("question", "Question to ask about the image", kValueTypeString, false)
        }), [this](const ParameterList& parameters) {
            std::string question = current_question_;
            try {
                question = parameters["question"].string();
            } catch (const std::runtime_error&) {
                // 参数不存在，使用默认值
            }

            // 立即拍照，参考MCP的实现
            auto& app = Application::GetInstance();
            app.Schedule([this, question]() {
                CaptureAndExplainImage(question);
            });
        });

        // 添加一个简单的拍照方法，完全参考MCP camera.take_photo的实现
        methods_.AddMethod("take_photo", "Take a photo and explain it, similar to MCP camera.take_photo", ParameterList({
            Parameter("question", "The question to ask about the photo", kValueTypeString, false)
        }), [this](const ParameterList& parameters) {
            std::string question = "请描述这张图片";
            try {
                question = parameters["question"].string();
            } catch (const std::runtime_error&) {
                // 参数不存在，使用默认值
            }

            ESP_LOGI(TAG, "Taking photo with question: %s", question.c_str());

            // 直接拍照并发送结果，完全参考MCP的实现
            auto& board = Board::GetInstance();
            auto camera = board.GetCamera();

            if (camera == nullptr) {
                ESP_LOGW(TAG, "No camera available on this board");
                return;
            }

            // 拍照
            if (!camera->Capture()) {
                ESP_LOGE(TAG, "Failed to capture photo");
                return;
            }

            // 获取识别结果并发送
            std::string result = camera->Explain(question);
            ESP_LOGI(TAG, "Photo explanation result: %s", result.c_str());

            // 发送结果到服务端
            auto& app = Application::GetInstance();
            app.SendTextMessage(result);
        });

        methods_.AddMethod("set_image_capture", "Enable or disable continuous image capture", ParameterList({
            Parameter("enabled", "Enable continuous image capture", kValueTypeBoolean, true)
        }), [this](const ParameterList& parameters) {
            image_capture_enabled_ = parameters["enabled"].boolean();
            ESP_LOGI(TAG, "Continuous image capture %s",
                     image_capture_enabled_ ? "enabled" : "disabled");

            if (current_mode_ != kWorkModeChat && image_capture_enabled_ && !waiting_for_tts_complete_) {
                // 如果启用连续捕获且当前没有在等待TTS，立即开始第一次捕获
                TriggerNextCapture();
            }
        });

        // 内部方法：接收TTS完成通知
        methods_.AddMethod("_notify_tts_complete", "Internal method to notify TTS completion", ParameterList(), [this](const ParameterList& parameters) {
            OnTTSComplete();
        });

        // 注册TTS状态监听
        RegisterTTSListener();
}

ModeController::~ModeController() {
    // 析构时清理
}

void ModeController::SwitchMode(WorkMode new_mode) {
    if (current_mode_ == new_mode) {
        ESP_LOGI(TAG, "Already in mode %d", static_cast<int>(new_mode));
        return;
    }

    WorkMode old_mode = current_mode_;
    current_mode_ = new_mode;
    waiting_for_tts_complete_ = false; // 重置等待状态

    ESP_LOGI(TAG, "Switching from mode %d to mode %d", static_cast<int>(old_mode), static_cast<int>(new_mode));

    // 通知应用程序模式变化
    auto& app = Application::GetInstance();
    app.Schedule([this, new_mode]() {
        OnModeChanged(new_mode);
    });
}

void ModeController::OnModeChanged(WorkMode new_mode) {
    // auto& board = Board::GetInstance();
    // auto display = board.GetDisplay();

    switch (new_mode) {
        case kWorkModeChat:
            // display->SetChatMessage("system", "已切换到对话模式");
            ESP_LOGI(TAG, "Switched to Chat Mode");
            break;
        case kWorkModeImageRecognition:
            // display->SetChatMessage("system", "已切换到识图模式");
            ESP_LOGI(TAG, "Switched to Image Recognition Mode");
            if (image_capture_enabled_) {
                TriggerNextCapture();
            }
            break;
        case kWorkModeNavigation:
            // display->SetChatMessage("system", "已切换到指路模式");
            ESP_LOGI(TAG, "Switched to Navigation Mode");
            if (image_capture_enabled_) {
                TriggerNextCapture();
            }
            break;
    }
}

void ModeController::RegisterTTSListener() {
    // 注册到Application的TTS状态监听
    // auto& app = Application::GetInstance();
    // 这里需要Application提供注册TTS监听的接口
    // 暂时先在CaptureAndExplainImage中设置等待状态
}

void ModeController::TriggerNextCapture() {
    if (current_mode_ == kWorkModeChat || !image_capture_enabled_ || waiting_for_tts_complete_) {
        return;
    }

    ESP_LOGI(TAG, "Triggering next image capture");
    auto& app = Application::GetInstance();
    app.Schedule([this]() {
        CaptureAndExplainImage(current_question_);
    });
}

void ModeController::OnTTSComplete() {
    ESP_LOGI(TAG, "TTS playback completed, triggering next capture");
    waiting_for_tts_complete_ = false;

    // 如果当前在识图或导航模式且启用了连续捕获，触发下一次捕获
    if ((current_mode_ == kWorkModeImageRecognition || current_mode_ == kWorkModeNavigation)
        && image_capture_enabled_) {
        TriggerNextCapture();
    }
}

void ModeController::CaptureAndExplainImage(const std::string& question) {
    ESP_LOGI(TAG, "Capturing and explaining image with question: %s", question.c_str());

    auto& board = Board::GetInstance();
    auto camera = board.GetCamera();

    if (camera == nullptr) {
        ESP_LOGW(TAG, "No camera available on this board");
        return;
    }

    // 先捕获图片
    if (!camera->Capture()) {
        ESP_LOGE(TAG, "Failed to capture image");
        return;
    }

    // 使用现有的Explain方法获取图片识别结果
    // 这会将图片发送到服务端的豆包视觉API进行处理
    std::string result = camera->Explain(question);

    ESP_LOGI(TAG, "Image explanation result: %s", result.c_str());

    // 如果启用了连续捕获，设置等待TTS完成状态
    if (image_capture_enabled_ && (current_mode_ == kWorkModeImageRecognition || current_mode_ == kWorkModeNavigation)) {
        waiting_for_tts_complete_ = true;
        ESP_LOGI(TAG, "Continuous capture enabled, waiting for TTS complete");
    }

    // 将结果通过现有通信协议发送给服务端进行TTS播放
    auto& app = Application::GetInstance();
    app.SendTextMessage(result);

    // 同时在显示屏上显示结果
    // auto display = board.GetDisplay();
    // if (display) {
    //     display->SetChatMessage("ai", result.c_str());
    // }
}

// 提供给Application调用的TTS完成通知方法
void ModeController::NotifyTTSComplete() {
    OnTTSComplete();
}

} // namespace iot

DECLARE_THING(ModeController);
