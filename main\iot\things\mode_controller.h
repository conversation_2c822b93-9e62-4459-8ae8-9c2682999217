#ifndef IOT_MODE_CONTROLLER_H
#define IOT_MODE_CONTROLLER_H

#include "iot/thing.h"

namespace iot {

// 设备工作模式枚举
enum WorkMode {
    kWorkModeChat = 0,      // 对话模式（默认）
    kWorkModeImageRecognition = 1,  // 识图模式
    kWorkModeNavigation = 2         // 指路模式
};

// 模式控制器IoT设备类
class ModeController : public Thing {
private:
    WorkMode current_mode_ = kWorkModeChat;
    bool image_capture_enabled_ = false;
    bool waiting_for_tts_complete_ = false; // 是否正在等待TTS播放完成
    std::string current_question_ = "请描述这张图片";

public:
    ModeController();
    ~ModeController();

    // 提供给Application调用的TTS完成通知方法
    void NotifyTTSComplete();

private:
    void SwitchMode(WorkMode new_mode);
    void OnModeChanged(WorkMode new_mode);
    void RegisterTTSListener();
    void TriggerNextCapture();
    void CaptureAndExplainImage(const std::string& question);
    void OnTTSComplete();
};

// 全局ModeController实例指针，用于Application直接访问
extern ModeController* g_mode_controller;

} // namespace iot

#endif // IOT_MODE_CONTROLLER_H
