package xiaozhi.modules.config.init;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import jakarta.annotation.PostConstruct;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.modules.config.service.ConfigService;
import xiaozhi.modules.sys.service.SysParamsService;

@Configuration
@DependsOn("liquibase")
public class SystemInitConfig {

    @Autowired
    private SysParamsService sysParamsService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private RedisUtils redisUtils;

    @PostConstruct
    public void init() {
        try {
            // 检查版本号
            String redisVersion = (String) redisUtils.get(RedisKeys.getVersionKey());
            if (!Constant.VERSION.equals(redisVersion)) {
                // 如果版本不一致，尝试清空Redis
                // 注意：如果Redis是只读副本，这里会抛出异常
                try {
                    redisUtils.emptyAll();
                    // 存储新版本号
                    redisUtils.set(RedisKeys.getVersionKey(), Constant.VERSION);
                } catch (Exception e) {
                    // 捕获Redis写入异常，记录日志但允许应用继续启动
                    System.err.println("警告: Redis写入操作失败，可能是连接到了只读副本。请检查Redis配置。");
                    System.err.println("错误详情: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            // 捕获所有Redis操作异常，允许应用继续启动
            System.err.println("警告: Redis操作失败。请检查Redis配置。");
            System.err.println("错误详情: " + e.getMessage());
        }

        // 继续执行其他初始化操作
        sysParamsService.initServerSecret();
        configService.getConfig(false);
    }
}