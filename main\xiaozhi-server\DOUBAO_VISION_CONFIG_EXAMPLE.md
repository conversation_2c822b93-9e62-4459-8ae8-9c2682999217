# 豆包视觉配置示例

## 按原版方式配置

现在豆包视觉模块按照原版的方式，通过 `plugins.eye_doubao.llm_code` 读取配置，从API项目获取参数。

## 配置方式

### 1. 在 config.yaml 中配置

```yaml
# 插件配置
plugins:
  eye_doubao:
    llm_code: "DoubaoVisionLLM"  # 指向LLM配置的名称

# LLM配置
LLM:
  DoubaoVisionLLM:
    type: openai
    api_key: "你的豆包视觉API密钥"
    base_url: "https://ark.cn-beijing.volces.com/api/v3"
    model_name: "ep-20241230140547-8xqzr"
    max_tokens: 1000
    temperature: 0.7
```

### 2. 或者从管理后台API获取

如果启用了API配置获取：

```yaml
read_config_from_api: true

selected_module:
  Vision: "DoubaoVision"
```

然后在管理后台配置：

```json
{
  "Vision": {
    "DoubaoVision": {
      "api_key": "你的豆包视觉API密钥",
      "base_url": "https://ark.cn-beijing.volces.com/api/v3",
      "model": "ep-20241230140547-8xqzr",
      "max_tokens": 1000,
      "temperature": 0.7
    }
  }
}
```

## 启动时的连通性测试

### 成功示例

```
🚀 豆包视觉模块已加载
📡 发现豆包视觉配置，开始连通性测试...
使用豆包视觉配置，模型: ep-20241230140547-8xqzr
正在测试豆包视觉API连通性...
豆包视觉LLM返回: 连接正常
豆包视觉API连通性测试成功
✅ 豆包视觉API连通性测试通过
📝 支持的功能：拍照识图
```

### 配置不完整示例

```
🚀 豆包视觉模块已加载
💡 未配置plugins.eye_doubao.llm_code，请配置后重启
📝 支持的功能：拍照识图
```

## 配置优先级

1. **本地配置优先**: 首先检查 `plugins.eye_doubao.llm_code` 指向的LLM配置
2. **API配置备用**: 如果本地配置不可用，且启用了API配置获取，则从管理后台获取
3. **错误提示**: 如果都不可用，给出明确的配置提示

## 运行时配置获取

运行时也按照相同的优先级获取配置：

1. 检查 `plugins.eye_doubao.llm_code`
2. 从对应的LLM配置中读取参数
3. 如果不可用，尝试从API获取
4. 进行连通性测试并打印LLM返回值

## 使用方法

配置完成后，可以通过语音命令使用：

- **"拍照识图"** - 进行图片识别

或者在代码中调用：

```python
from plugins_func.functions.isee_eyes_doubao import eyes_doubaoreal

# 调用豆包视觉识别
result = eyes_doubaoreal(conn, mode=1, image_path="path/to/image.jpg")
```

## 注意事项

1. **配置名称**: `llm_code` 必须指向一个有效的LLM配置名称
2. **API密钥**: LLM配置中必须包含有效的 `api_key`
3. **模型名称**: 使用 `model_name` 字段（不是 `model`）
4. **连通性测试**: 启动时会自动测试LLM连通性并打印返回值
5. **错误处理**: 配置错误不会影响服务器启动，但会给出明确提示

## 总结

现在豆包视觉模块完全按照原版的方式工作：

- 通过 `plugins.eye_doubao.llm_code` 读取配置
- 从API项目获取参数（如果启用）
- 启动时进行真实的LLM连通性测试
- 打印LLM的实际返回值
- 提供清晰的配置指导
