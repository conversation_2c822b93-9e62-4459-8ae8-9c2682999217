# 豆包视觉识别功能

## 概述

基于豆包视觉大模型的图片识别功能，配置从管理后台API动态获取。

## 功能特性

- ✅ 从管理后台API动态获取豆包视觉配置
- ✅ 首次使用时自动进行连通性测试
- ✅ 支持拍照识图功能
- ✅ 完整的错误处理和日志记录

## 配置方式

### 管理后台配置

在管理后台的代理模型配置中添加豆包视觉配置：

```json
{
  "Vision": {
    "DoubaoVision": {
      "api_key": "你的豆包视觉API密钥",
      "base_url": "https://ark.cn-beijing.volces.com/api/v3",
      "model": "ep-20241230140547-8xqzr",
      "max_tokens": 1000,
      "temperature": 0.7
    }
  }
}
```

## 使用方法

### 语音命令

- **"拍照识图"** - 进行单次拍照识别

### 函数调用

```python
# 在代码中调用
from plugins_func.functions.isee_eyes_doubao import eyes_doubaoreal

# 调用豆包视觉识别
result = eyes_doubaoreal(conn, mode=1, image_path="path/to/image.jpg")
```

## 工作流程

```
1. 用户调用拍照识图功能
   ↓
2. 系统从管理后台API获取豆包视觉配置
   ↓
3. 首次使用时进行连通性测试
   ↓
4. 读取图片文件并转换为base64
   ↓
5. 调用豆包视觉API进行图片分析
   ↓
6. 返回识别结果并进行TTS播报
```

## 日志输出

### 模块加载时
```
🚀 豆包视觉模块已加载
💡 豆包视觉配置将从管理后台API动态获取
📝 支持的功能：拍照识图
🔧 连通性测试将在首次使用时进行
```

### 首次使用时
```
从API获取豆包视觉配置成功，模型: ep-20241230140547-8xqzr
首次使用豆包视觉，进行连通性测试...
豆包视觉API连通性测试成功: 连接正常
✅ 豆包视觉API连通性测试通过
```

### 图片识别时
```
豆包视觉分析成功: 我看到一张图片，显示了...
```

## 错误处理

### 常见问题

1. **API配置获取失败**
   ```
   从API获取豆包视觉配置失败: 设备未找到
   ```
   - 检查设备是否在管理后台正确注册
   - 确认设备ID和客户端ID正确

2. **API密钥无效**
   ```
   豆包视觉API连通性测试失败: 401, {"error": "Invalid API key"}
   ```
   - 检查管理后台配置的API密钥是否正确
   - 确认API密钥是否有效且未过期

3. **网络连接问题**
   ```
   豆包视觉API连通性测试超时
   ```
   - 检查网络连接是否正常
   - 确认防火墙设置

4. **图片文件问题**
   ```
   图片文件不存在: /path/to/image.jpg
   ```
   - 确认图片文件路径正确
   - 检查文件是否存在且可读

## API接口

### eyes_doubaoreal函数

```python
def eyes_doubaoreal(conn, mode, image_path):
    """
    豆包视觉识别主函数
    
    Args:
        conn: 连接对象
        mode: 模式 (1=识图, 2=指路)
        image_path: 图片路径
    
    Returns:
        ActionResponse: 包含识别结果的响应对象
    """
```

### capture_and_analyze_image函数

```python
def capture_and_analyze_image(conn, question="请描述这张图片"):
    """
    拍照识图功能
    
    Args:
        conn: 连接对象
        question: 对图片询问的问题
    
    Returns:
        ActionResponse: 包含处理状态的响应对象
    """
```

## 技术细节

### 连通性测试

连通性测试使用1x1像素的白色测试图片，验证豆包视觉API是否可用：

```python
# 测试图片（1x1像素白色PNG）
test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
```

### 配置获取

系统会自动从管理后台API获取配置：

```python
# 调用管理API获取配置
api_config = get_agent_models(device_id, client_id, selected_module)
vision_config = api_config["Vision"].get("DoubaoVision", {})
```

### 错误重试

API调用失败时会自动重试，重试机制由`manage_api_client`模块处理。

## 注意事项

1. **配置来源**：配置完全从管理后台API获取，不使用本地配置文件
2. **连通性测试**：只测试API是否可用，与设备ID无关
3. **首次使用**：首次使用时会自动进行连通性测试
4. **异步处理**：图片分析采用异步处理，不阻塞主线程
5. **错误处理**：完整的错误处理机制，确保系统稳定性

## 总结

豆包视觉识别功能提供了完整的图片识别能力，通过管理后台API动态配置，支持自动连通性测试，具有良好的错误处理机制。用户只需在管理后台配置好豆包视觉API参数，即可通过语音命令"拍照识图"使用该功能。
