# 豆包视觉启动连通性测试

## 功能说明

现在豆包视觉模块在启动时会自动进行连通性测试，让您在项目启动时就知道LLM是否可用，而不是使用时才发现问题。

## 测试逻辑

### 1. 如果启用了API配置获取
```
🚀 豆包视觉模块已加载
💡 豆包视觉配置将从管理后台API动态获取
🔧 连通性测试将在首次使用时进行（需要真实设备连接）
📝 支持的功能：拍照识图
```

### 2. 如果有本地配置且API密钥存在
```
🚀 豆包视觉模块已加载
📡 发现本地豆包视觉配置，开始连通性测试...
豆包视觉API连通性测试成功: 连接正常
✅ 豆包视觉API连通性测试通过
📝 支持的功能：拍照识图
```

### 3. 如果没有配置
```
🚀 豆包视觉模块已加载
💡 未发现本地豆包视觉配置
📝 支持的功能：拍照识图
```

## 本地配置测试

如果您想在启动时测试连通性，可以在 `config.yaml` 中添加：

```yaml
# 关闭API配置获取，使用本地配置进行启动测试
read_config_from_api: false

# 豆包视觉配置
Vision:
  DoubaoVision:
    api_key: "你的豆包视觉API密钥"
    base_url: "https://ark.cn-beijing.volces.com/api/v3"
    model: "ep-20241230140547-8xqzr"
    max_tokens: 1000
    temperature: 0.7
```

## 启动测试结果

### 成功示例
```
250601 04:45:00[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-INFO-🚀 豆包视觉模块已加载
250601 04:45:00[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-INFO-📡 发现本地豆包视觉配置，开始连通性测试...
250601 04:45:01[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-INFO-使用本地豆包视觉配置，模型: ep-20241230140547-8xqzr
250601 04:45:02[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-INFO-豆包视觉API连通性测试成功: 连接正常
250601 04:45:02[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-INFO-✅ 豆包视觉API连通性测试通过
250601 04:45:02[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-INFO-📝 支持的功能：拍照识图
```

### 失败示例
```
250601 04:45:00[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-INFO-🚀 豆包视觉模块已加载
250601 04:45:00[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-INFO-📡 发现本地豆包视觉配置，开始连通性测试...
250601 04:45:01[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-INFO-使用本地豆包视觉配置，模型: ep-20241230140547-8xqzr
250601 04:45:02[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-ERROR-豆包视觉API连通性测试失败: 401, {"error": "Invalid API key"}
250601 04:45:02[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-WARNING-⚠️  豆包视觉API连通性测试失败
250601 04:45:02[0.5.2-00000000000000][plugins_func.functions.isee_eyes_doubao]-INFO-📝 支持的功能：拍照识图
```

## 优势

1. **启动时就知道问题** - 不用等到使用时才发现API不可用
2. **简单直接** - 不需要额外的测试脚本或复杂配置
3. **智能判断** - 根据配置情况自动选择测试策略
4. **不影响启动** - 测试失败不会阻塞服务器启动
5. **清晰日志** - 详细的状态信息，便于问题排查

## 注意事项

1. **API配置优先** - 如果启用了API配置获取，连通性测试会在首次使用时进行
2. **本地配置测试** - 只有在本地配置完整时才会进行启动测试
3. **异步处理** - 连通性测试采用异步方式，不阻塞启动流程
4. **错误容忍** - 测试失败不会影响模块加载和服务器启动

## 总结

现在您可以在项目启动时就知道豆包视觉LLM的连通性状态，无需等到实际使用时才发现问题。这样可以提前发现配置问题，提高系统的可靠性。
