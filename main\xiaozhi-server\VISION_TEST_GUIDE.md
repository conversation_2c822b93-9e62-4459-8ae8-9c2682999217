# 豆包视觉功能测试指南

## 当前状态

✅ **豆包视觉模块已加载** - 模块成功加载，配置将从管理后台API动态获取
✅ **连通性测试机制** - 首次使用时自动进行API连通性测试
✅ **HTTP服务器就绪** - 视觉HTTP服务器代码已准备完成

## 测试步骤

### 1. 配置管理后台

在管理后台的代理模型配置中添加豆包视觉配置：

```json
{
  "Vision": {
    "DoubaoVision": {
      "api_key": "你的豆包视觉API密钥",
      "base_url": "https://ark.cn-beijing.volces.com/api/v3", 
      "model": "ep-20241230140547-8xqzr",
      "max_tokens": 1000,
      "temperature": 0.7
    }
  }
}
```

### 2. 启动视觉HTTP服务器

```bash
cd main/xiaozhi-server
python start_vision_server.py
```

预期输出：
```
🚀 启动视觉HTTP服务器测试...
视觉HTTP服务器已启动，端口: 8003
✅ 视觉HTTP服务器已启动，按Ctrl+C退出
```

### 3. 测试图片上传

在另一个终端运行：

```bash
cd main/xiaozhi-server
python test_vision_upload.py
```

预期输出：
```
🧪 豆包视觉HTTP服务器测试
🔍 测试健康检查: http://localhost:8003/xiaozhi/vision/health
✅ 健康检查通过: {"status": "ok", "message": "视觉HTTP服务器运行正常"}

🚀 正在测试视觉服务器: http://localhost:8003/xiaozhi/vision/explain
📷 上传测试图片，大小: 67 字节
📡 响应状态: 200
✅ 请求成功!
🎉 视觉识别成功: [豆包视觉API的识别结果]
```

### 4. 通过语音命令测试

启动完整的xiaozhi-server后，通过语音命令测试：

```
用户："小智，拍照识图"
```

预期流程：
```
1. 系统从管理后台API获取豆包视觉配置
2. 首次使用时进行连通性测试
3. 调用豆包视觉API分析图片
4. TTS播报识别结果
```

## 完整集成步骤

### 1. 在主应用程序中启动视觉HTTP服务器

在 `app.py` 中添加：

```python
from vision_http_server import start_vision_http_server

async def main():
    config = load_config()
    
    # 启动视觉HTTP服务器
    await start_vision_http_server(config)
    
    # 其他启动逻辑...
```

### 2. 配置ESP32摄像头

ESP32需要设置摄像头的解释URL：

```cpp
// 在ESP32初始化代码中
auto camera = board.GetCamera();
if (camera) {
    camera->SetExplainUrl("http://服务器IP:8003/xiaozhi/vision/explain", "");
}
```

### 3. 验证完整流程

1. **模块加载** - 查看启动日志确认豆包视觉模块已加载
2. **配置获取** - 首次使用时从管理后台API获取配置
3. **连通性测试** - 自动测试豆包视觉API连通性
4. **图片识别** - ESP32拍照上传，服务端调用豆包视觉API
5. **结果播报** - TTS播报识别结果

## 日志监控

### 成功的日志示例

**模块加载时：**
```
🚀 豆包视觉模块已加载
💡 豆包视觉配置将从管理后台API动态获取
📝 支持的功能：拍照识图
🔧 连通性测试将在首次使用时进行
```

**首次使用时：**
```
从API获取豆包视觉配置成功，模型: ep-20241230140547-8xqzr
首次使用豆包视觉，进行连通性测试...
豆包视觉API连通性测试成功: 连接正常
✅ 豆包视觉API连通性测试通过
```

**图片识别时：**
```
收到图片解释请求，设备ID: ESP32_001, 会话ID: session_001
图片已保存: tmp/vision_session_001_ESP32_001.jpg, 大小: 12345 字节
豆包视觉分析成功: 我看到一张图片，显示了...
```

### 错误排查

**配置获取失败：**
```
从API获取豆包视觉配置失败: 设备未找到
```
- 检查设备是否在管理后台正确注册
- 确认设备ID和客户端ID正确

**API连通性失败：**
```
豆包视觉API连通性测试失败: 401, {"error": "Invalid API key"}
```
- 检查管理后台配置的API密钥是否正确
- 确认API密钥是否有效且未过期

## 当前实现状态

✅ **豆包视觉模块** - 完成，支持从管理后台API获取配置
✅ **连通性测试** - 完成，首次使用时自动测试
✅ **HTTP服务器** - 完成，支持接收ESP32图片上传
✅ **图片识别** - 完成，集成豆包视觉API
✅ **错误处理** - 完成，完整的错误处理机制
✅ **测试工具** - 完成，提供测试脚本

🔄 **待完成项目：**
- 在主应用程序中集成视觉HTTP服务器
- 配置ESP32摄像头的解释URL
- 端到端测试验证

## 总结

豆包视觉功能已经完成核心实现：

1. **配置管理** - 从管理后台API动态获取配置
2. **连通性测试** - 自动测试API可用性
3. **图片处理** - 完整的图片上传和识别流程
4. **错误处理** - 完善的错误处理和日志记录

用户现在可以：
1. 在管理后台配置豆包视觉API参数
2. 启动视觉HTTP服务器进行测试
3. 通过测试脚本验证功能
4. 集成到完整的xiaozhi-server中使用

下一步只需要在主应用程序中启动视觉HTTP服务器，并配置ESP32摄像头URL，即可实现完整的豆包视觉识别功能！
