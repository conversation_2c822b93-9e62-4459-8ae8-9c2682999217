import openai
from openai.types import CompletionUsage
from config.logger import setup_logging
from core.utils.util import check_model_key
from core.providers.llm.base import LLMProviderBase
import base64
import os
import json
import re
from http.server import HTTPServer, BaseHTTPRequestHandler
from datetime import datetime
from core.handle.iotHandle import send_iot_conn
import asyncio
import threading
import time
from PIL import Image

TAG = __name__
logger = setup_logging()

# 图片转base64函数


def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def mask_api_key(api_key):
    """安全地掩盖 API 密钥，只显示前4位和后4位"""
    if not api_key or len(api_key) < 8:
        return "无效密钥"
    return f"{api_key[:4]}...{api_key[-4:]}"


def sanitize_error_message(message):
    """从错误消息中移除敏感信息"""
    # 移除可能包含 API 密钥的部分
    message = re.sub(r'(api[-_]?key|token)[=:]\s*["\']?\w+["\']?',
                     r'\1=***', message, flags=re.IGNORECASE)
    return message


class LLMProvider(LLMProviderBase):
    def __init__(self, config):
        self.model_name = config.get("model_name")
        self.api_key = config.get("api_key")
        if "base_url" in config:
            self.base_url = config.get("base_url")
        else:
            self.base_url = config.get("url")
        max_tokens = config.get("max_tokens")
        if max_tokens is None or max_tokens == "":
            max_tokens = 500

        try:
            max_tokens = int(max_tokens)
        except (ValueError, TypeError):
            max_tokens = 500
        self.max_tokens = max_tokens
        check_model_key("LLM", self.api_key)
        self.client = openai.OpenAI(api_key=self.api_key, base_url=self.base_url)

    def response(self, session_id, dialogue):
        # 记录配置状态（不记录敏感信息）
        logger.bind(tag=TAG).info(f"OpenAI 配置状态:")
        logger.bind(tag=TAG).info(f"- 模型名称: {self.model_name}")
        logger.bind(tag=TAG).info(f"- API 密钥存在: {bool(self.api_key)}")
        logger.bind(tag=TAG).info(
            f"- API 密钥长度: {len(self.api_key) if self.api_key else 0}")
        if self.api_key:
            masked_key = mask_api_key(self.api_key)
            logger.bind(tag=TAG).debug(f"- API 密钥掩码: {masked_key}")
        logger.bind(tag=TAG).info(f"- 基础 URL 存在: {bool(self.base_url)}")
        if self.base_url:
            logger.bind(tag=TAG).debug(f"- 基础 URL 前缀: {self.base_url[:10]}...")
        logger.bind(tag=TAG).info(f"- 最大 Token 数: {self.max_tokens}")

        # 检查 API 密钥格式
        if self.api_key and not self.api_key.startswith("sk-"):
            logger.bind(tag=TAG).warning(
                "API 密钥格式可能不正确，OpenAI API 密钥通常以 'sk-' 开头")

        check_model_key("LLM", self.api_key)
        try:
            self.client = openai.OpenAI(
                api_key=self.api_key, base_url=self.base_url)
            logger.bind(tag=TAG).info("OpenAI 客户端初始化成功")

            # 启动HTTP服务器
            # self._start_http_server()

        except Exception as e:
            error_type = type(e).__name__
            error_message = sanitize_error_message(str(e))
            logger.bind(tag=TAG).error(
                f"OpenAI 客户端初始化失败: {error_type}: {error_message}")
            raise


    def _format_error_for_user(self, error_type, error_message):
        """格式化错误消息，避免将敏感信息发送给客户端

        Args:
            error_type: 错误类型
            error_message: 错误消息

        Returns:
            str: 格式化后的用户友好错误消息
        """
        # 根据错误类型提供用户友好的错误消息
        if "401" in error_message or "AuthenticationError" in error_type:
            return "【系统提示：AI服务暂时无法访问，请联系管理员检查API配置】"
        elif "404" in error_message:
            return "【系统提示：AI服务端点无法访问，请联系管理员检查网络或API配置】"
        elif "429" in error_message:
            return "【系统提示：AI服务请求过多，请稍后再试】"
        elif "model_not_found" in error_message.lower() or "模型不存在" in error_message:
            return f"【系统提示：AI模型暂时不可用，请联系管理员检查配置】"
        elif "tools" in error_message.lower() and "type" in error_message.lower():
            return "【系统提示：AI工具配置有误，请联系管理员】"
        elif "timeout" in error_message.lower():
            return "【系统提示：AI服务响应超时，请稍后再试】"
        else:
            # 对于未知错误，提供通用错误消息
            return f"【系统提示：AI服务暂时不可用，请稍后再试】"



    def response(self, session_id, dialogue, conn = None):
        use_pic = False
        # 处理图片上传
        try:
            # if conn is not None:
            #     url = f"http://{conn.client_ip}/jpg"
            # else:
            #     url = 'http://***************/jpg'  # 改成你自己的板子ip
            # # 发送请求
            # response = requests.get(url, timeout=1000)
            # use_pic = False
            # if response.status_code == 200:
            #     with open('tmp/image.jpg', 'wb') as file:
            #         file.write(response.content)
            #         print('文件下载成功')
            #         use_pic = True
            # else:
            #     print('文件下载失败')
            logger.bind(tag=TAG).info("openai什么情况下会调用这个方法？？？ begin to take photo")
            print("openai为什么会调用这个方法？？？ begin to take photo")
            # 不使用 asyncio.run()，因为我们已经在一个事件循环中
            asyncio.run(send_iot_conn(conn, "Camera", "take_photo", {}))
            # 直接使用已上传的图片，跳过拍照步骤
            logger.bind(tag=TAG).info("使用已上传的图片，跳过拍照步骤")
            print("openai begin to process uploaded photo")
            # 开启服务器, 获取图片
            # 2. 等待并检查图片上传（最多等待5秒）
            print("检查图片是否存在...")
            start_time = time.time()
            while not os.path.exists('tmp/image.jpg'):
                time.sleep(0.2)
                if time.time() - start_time > 5:
                    print("等待图片超时")
                    break

            # 3. 检查结果
            if os.path.exists('tmp/image.jpg'):
                use_pic = True
                logger.bind(tag=TAG).info("图片上传验证成功")
            else:
                logger.bind(tag=TAG).error("未检测到上传的图片")
        except Exception as e:
            logger.bind(tag=TAG).error(f"Error in response generation: {e}")
            return "【图片服务响应异常】"

        if use_pic == True:
            # mirror_image('tmp/image.jpg', 'tmp/flipped_image.jpg')
            image_path = "tmp/image.jpg"
            #原图片转base64
            base64_image = encode_image(image_path)

            last_message = dialogue[-1]
            content_text = last_message.get("content", "")
            content_img = [
                {"type": "text", "text": content_text},
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}"
                    },
                },
            ]
            dialogue[-1]["content"] = content_img
        try:
            responses = self.client.chat.completions.create(
                model=self.model_name,
                messages=dialogue,
                stream=True,
                max_tokens=self.max_tokens,
            )

            is_active = True
            for chunk in responses:
                try:
                    # 检查是否存在有效的choice且content不为空
                    delta = chunk.choices[0].delta if getattr(chunk, 'choices', None) else None
                    content = delta.content if hasattr(delta, 'content') else ''
                except IndexError:
                    content = ''
                if content:
                    # 处理标签跨多个chunk的情况
                    if '<think>' in content:
                        is_active = False
                        content = content.split('<think>')[0]
                    if '</think>' in content:
                        is_active = True
                        content = content.split('</think>')[-1]
                    if is_active:
                        # 只在调试模式下打印内容
                        # print("content", content, end="")
                        yield content

        except Exception as e:
            logger.bind(tag=TAG).error(f"Error in response generation: {e}")


        # 删除临时图片
        if os.path.exists('tmp/image.jpg'):
            os.remove('tmp/image.jpg')

    def response_with_functions(self, session_id, dialogue, functions=None):
        """生成带函数调用的对话响应

        Args:
            session_id: 会话ID，用于日志记录
            dialogue: 对话历史
            functions: 可用的函数列表

        Yields:
            tuple: (生成的响应内容, 工具调用)
        """
        logger.bind(tag=TAG).info(f"开始生成带函数调用的响应，会话ID: {session_id}")
        logger.bind(tag=TAG).debug(f"对话长度: {len(dialogue)} 轮")
        logger.bind(tag=TAG).debug(
            f"函数数量: {len(functions) if functions else 0}")

        try:
            # 记录请求参数
            logger.bind(tag=TAG).debug(f"请求参数: 模型={self.model_name}")

            # 记录请求消息（只记录前两轮对话，避免日志过长）
            truncated_dialogue = dialogue[:2] if len(
                dialogue) > 2 else dialogue
            logger.bind(tag=TAG).debug(
                f"请求消息 --> {json.dumps(truncated_dialogue, ensure_ascii=False)}")

            # 记录函数信息
            if functions:
                truncated_functions = functions[:2] if len(
                    functions) > 2 else functions
                logger.bind(tag=TAG).debug(
                    f"函数信息 --> {json.dumps(truncated_functions, ensure_ascii=False)}")

            stream = self.client.chat.completions.create(
                model=self.model_name, messages=dialogue, stream=True, tools=functions
            )

            for chunk in stream:
                # 检查是否存在有效的choice且content不为空
                if getattr(chunk, "choices", None):
                    yield chunk.choices[0].delta.content, chunk.choices[0].delta.tool_calls
                # 存在 CompletionUsage 消息时，生成 Token 消耗 log
                elif isinstance(getattr(chunk, 'usage', None), CompletionUsage):
                    usage_info = getattr(chunk, 'usage', None)
                    logger.bind(tag=TAG).info(
                        f"Token 消耗：输入 {getattr(usage_info, 'prompt_tokens', '未知')}，"
                        f"输出 {getattr(usage_info, 'completion_tokens', '未知')}，"
                        f"共计 {getattr(usage_info, 'total_tokens', '未知')}"
                    )

        except Exception as e:
            logger.bind(tag=TAG).error(f"Error in function call streaming: {e}")
            yield f"【OpenAI服务响应异常: {e}】", None
            # 记录详细的错误信息，但不包括敏感数据
            error_type = type(e).__name__
            error_message = sanitize_error_message(str(e))

            # 记录更详细的配置信息，帮助调试
            logger.bind(tag=TAG).error(
                f"带函数调用的响应生成失败: {error_type}: {error_message}")
            logger.bind(tag=TAG).error(f"使用的模型: {self.model_name}")

            # 记录 API 密钥信息（安全方式）
            if self.api_key:
                masked_key = mask_api_key(self.api_key)
                logger.bind(tag=TAG).error(f"API 密钥: {masked_key}")
            else:
                logger.bind(tag=TAG).error("API 密钥为空")

            # 记录 API 端点信息
            if self.base_url:
                logger.bind(tag=TAG).error(f"API 端点: {self.base_url}")
            else:
                logger.bind(tag=TAG).error("API 端点为空")

            # 添加更多诊断信息
            if "401" in error_message:
                logger.bind(tag=TAG).error("认证错误 (401)：API 密钥可能无效或缺失")
                if self.api_key:
                    logger.bind(tag=TAG).error(
                        f"API 密钥前缀: {self.api_key[:4]}...")
                    logger.bind(tag=TAG).error(
                        f"API 密钥长度: {len(self.api_key)}")
                    # 检查密钥格式
                    if not self.api_key.startswith("sk-"):
                        logger.bind(tag=TAG).error(
                            "API 密钥格式可能不正确，OpenAI API 密钥通常以 'sk-' 开头")
                else:
                    logger.bind(tag=TAG).error("API 密钥为空")
            elif "404" in error_message:
                logger.bind(tag=TAG).error("未找到错误 (404)：API 端点可能不正确")
                if self.base_url:
                    logger.bind(tag=TAG).error(f"基础 URL: {self.base_url}")
            elif "429" in error_message:
                logger.bind(tag=TAG).error("请求过多错误 (429)：已超过 API 速率限制")
            elif "model_not_found" in error_message.lower() or "模型不存在" in error_message:
                logger.bind(tag=TAG).error(
                    f"模型错误：指定的模型 '{self.model_name}' 可能不存在或不可用")
            elif "tools" in error_message.lower() and "type" in error_message.lower():
                logger.bind(tag=TAG).error("工具格式错误：工具定义可能缺少 'type' 字段")
                if functions:
                    logger.bind(tag=TAG).error(
                        f"函数示例: {functions[0] if functions else 'None'}")

            # 格式化错误消息，避免将敏感信息发送给客户端
            user_friendly_error = self._format_error_for_user(
                error_type, error_message)
            yield user_friendly_error, None
