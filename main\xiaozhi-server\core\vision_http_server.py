"""
视觉HTTP服务器
接收ESP32摄像头上传的图片并进行处理
"""

import os
import asyncio
import json
from aiohttp import web, multipart
from loguru import logger

TAG = __name__

class VisionHttpServer:
    """视觉HTTP服务器"""
    
    def __init__(self, port=8003):
        self.port = port
        self.app = None
        self.runner = None
        self.site = None
        
    async def start(self):
        """启动HTTP服务器"""
        self.app = web.Application()
        
        # 添加路由
        self.app.router.add_post('/vision/explain', self.handle_explain)
        self.app.router.add_get('/vision/health', self.handle_health)
        
        # 启动服务器
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        self.site = web.TCPSite(self.runner, '0.0.0.0', self.port)
        await self.site.start()
        
        logger.bind(tag=TAG).info(f"视觉HTTP服务器已启动，端口: {self.port}")
        
    async def stop(self):
        """停止HTTP服务器"""
        if self.site:
            await self.site.stop()
        if self.runner:
            await self.runner.cleanup()
            
    async def handle_explain(self, request):
        """处理图片解释请求"""
        try:
            # 获取设备ID和会话ID
            device_id = request.headers.get('Device-ID', 'unknown')
            session_id = request.headers.get('Session-ID', 'default')
            
            logger.bind(tag=TAG).info(f"收到图片解释请求，设备ID: {device_id}, 会话ID: {session_id}")
            
            # 解析multipart/form-data
            reader = multipart.MultipartReader.from_response(request)
            question = "请描述这张图片"
            image_data = None
            
            async for part in reader:
                if part.name == 'question':
                    question = await part.text()
                elif part.name == 'file':
                    image_data = await part.read()
                    
            if not image_data:
                return web.Response(
                    text=json.dumps({"success": False, "message": "未收到图片数据"}),
                    content_type="application/json",
                    status=400
                )
                
            # 保存图片
            os.makedirs("tmp", exist_ok=True)
            image_path = f"tmp/vision_{session_id}_{device_id}.jpg"
            
            with open(image_path, 'wb') as f:
                f.write(image_data)
                
            logger.bind(tag=TAG).info(f"图片已保存: {image_path}, 大小: {len(image_data)} 字节, 问题: {question}")
            
            # 处理图片
            result = await self.process_image(image_path, question, device_id, session_id)
            
            return web.Response(
                text=json.dumps(result),
                content_type="application/json"
            )
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"处理图片解释请求失败: {e}")
            return web.Response(
                text=json.dumps({"success": False, "message": f"处理失败: {str(e)}"}),
                content_type="application/json",
                status=500
            )
            
    async def process_image(self, image_path, question, device_id, session_id):
        """处理图片"""
        try:
            # 导入视觉处理函数
            from plugins_func.functions.eyes_doubao import eyes_doubaoreal
            
            # 确定模式
            mode = 1  # 默认识图模式
            if "路况" in question or "导航" in question or "指路" in question:
                mode = 2  # 指路模式
                
            # 创建模拟连接对象
            class MockConnection:
                def __init__(self, device_id, session_id):
                    self.device_id = device_id
                    self.session_id = session_id
                    self.prompt = question
                    
            mock_conn = MockConnection(device_id, session_id)
            
            # 调用视觉处理函数
            result = eyes_doubaoreal(mock_conn, mode, image_path)
            
            if result and hasattr(result, 'response') and result.response:
                # 通知连续视觉模块
                await self.notify_vision_result(device_id, session_id, result.response)
                
                return {
                    "success": True,
                    "result": result.response,
                    "mode": mode,
                    "question": question
                }
            else:
                return {
                    "success": False,
                    "message": "图片处理失败或结果为空"
                }
                
        except Exception as e:
            logger.bind(tag=TAG).error(f"处理图片时出错: {e}")
            return {
                "success": False,
                "message": f"处理图片时出错: {str(e)}"
            }
            
    async def notify_vision_result(self, device_id, session_id, result_text):
        """通知连续视觉模块处理结果"""
        try:
            # 这里可以通过WebSocket或其他方式通知连续视觉模块
            # 暂时先记录日志
            logger.bind(tag=TAG).info(f"视觉处理结果: 设备{device_id}, 会话{session_id}, 结果: {result_text[:100]}...")
            
            # TODO: 实现与连续视觉模块的通信
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"通知视觉结果失败: {e}")
            
    async def handle_health(self, request):
        """健康检查"""
        return web.Response(
            text=json.dumps({"status": "ok", "message": "视觉HTTP服务器运行正常"}),
            content_type="application/json"
        )


# 全局服务器实例
vision_http_server = None

async def start_vision_http_server(port=8003):
    """启动视觉HTTP服务器"""
    global vision_http_server
    
    if vision_http_server is None:
        vision_http_server = VisionHttpServer(port)
        await vision_http_server.start()
        logger.bind(tag=TAG).info(f"视觉HTTP服务器已启动在端口 {port}")
    else:
        logger.bind(tag=TAG).warning("视觉HTTP服务器已经在运行")
        
async def stop_vision_http_server():
    """停止视觉HTTP服务器"""
    global vision_http_server
    
    if vision_http_server:
        await vision_http_server.stop()
        vision_http_server = None
        logger.bind(tag=TAG).info("视觉HTTP服务器已停止")
    else:
        logger.bind(tag=TAG).warning("视觉HTTP服务器未运行")


def get_vision_server_url(host="localhost", port=8003):
    """获取视觉服务器URL"""
    return f"http://{host}:{port}/vision/explain"
