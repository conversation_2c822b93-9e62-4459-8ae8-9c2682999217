# 连续视觉识别功能使用指南

## 概述

基于您现有的HTTP服务和`eyes_doubaoreal`函数，实现了连续视觉识别功能。用户可以通过语音命令启用识图模式或指路模式，设备会自动循环拍照、识别、播报。

## 核心特性

- **智能循环**：播报完成后自动拍下一张照片
- **模式切换**：支持识图模式和指路模式
- **语音控制**：通过自然语言启用/退出
- **无缝集成**：基于现有HTTP服务，无需修改ESP32代码

## 工作原理

```
用户语音命令 → 启用连续视觉模式 → 拍照 → 上传到HTTP服务 → 
调用eyes_doubaoreal → 豆包视觉分析 → TTS播报 → 播报完成 → 
自动拍下一张 → 循环继续...
```

## 支持的语音命令

### 启用识图模式
- "启用识图模式"
- "开始识图"
- "连续识图"

### 启用指路模式  
- "启用指路模式"
- "开始导航"
- "路况分析"

### 退出模式
- "退出视觉模式"
- "停止识图"
- "回到对话模式"

### 单次拍照
- "拍照识图"
- "拍一张照片"

## 使用示例

### 场景1：连续识图监控
```
用户："小智，启用识图模式"
系统："已启用识图模式，将连续拍照识别并询问：请描述这张图片"
[自动拍照] → [识别] → [播报："我看到一个桌子上放着..."]
[播报完成] → [自动拍下一张] → [识别] → [播报下一个结果]
...循环继续
```

### 场景2：导航助手
```
用户："小智，启用指路模式"  
系统："已启用指路模式，将连续分析前方路况并提供导航建议"
[自动拍照] → [分析路况] → [播报："前方是一个十字路口，建议..."]
[播报完成] → [自动拍下一张] → [分析] → [播报下一个路况]
...循环继续
```

### 场景3：退出模式
```
用户："小智，退出视觉模式"
系统："已退出视觉模式，回到正常对话模式"
[停止自动拍照循环]
```

## 技术实现

### 文件结构
```
xiaozhi-server/
├── plugins_func/functions/
│   └── continuous_vision.py          # 连续视觉插件
├── core/handle/
│   └── sendAudioHandle.py            # TTS处理（已修改）
└── docs/
    └── CONTINUOUS_VISION_GUIDE.md    # 本文档
```

### 核心类：VisionSession
```python
class VisionSession:
    def __init__(self, conn, mode, question):
        self.conn = conn
        self.mode = mode  # 1=识图, 2=指路
        self.question = question
        self.is_active = False
        self.is_waiting_tts = False
        
    def start(self):
        """启动连续识别"""
        
    def on_tts_complete(self):
        """TTS完成回调，触发下一次拍照"""
```

### 注册的函数
1. `启用识图模式` - 开始连续识图
2. `启用指路模式` - 开始连续导航
3. `退出视觉模式` - 停止连续模式
4. `拍照识图` - 单次拍照识别

## 集成步骤

### 1. 复制文件
将 `continuous_vision.py` 放到 `plugins_func/functions/` 目录

### 2. 修改TTS处理
在 `sendAudioHandle.py` 的 `send_tts_message` 函数中添加：
```python
# 如果是TTS停止，通知连续视觉模块
if state == "stop":
    try:
        from plugins_func.functions.continuous_vision import notify_tts_complete
        notify_tts_complete(conn)
    except ImportError:
        pass
```

### 3. 测试功能
启动服务器后，通过语音命令测试：
- "小智，启用识图模式"
- "小智，退出视觉模式"

## 配置选项

### 默认设置
- 识图模式问题：`"请描述这张图片"`
- 指路模式问题：`"请描述前方的路况和导航信息"`
- 拍照间隔：播报完成后延迟1秒
- 图片等待超时：10秒

### 自定义问题
```python
# 启用识图模式时可以自定义问题
start_image_recognition_mode(conn, question="请详细分析这张图片的安全隐患")
```

## 状态管理

### 全局状态
```python
vision_sessions = {}  # 存储每个连接的视觉会话状态
```

### 会话状态
- `is_active`: 是否处于活跃状态
- `is_waiting_tts`: 是否正在等待TTS播放完成
- `mode`: 当前模式（1=识图，2=指路）

## 错误处理

### 常见问题及解决方案

1. **拍照失败**
   - 检查ESP32摄像头是否正常
   - 确认IoT Camera设备已注册

2. **图片上传超时**
   - 检查HTTP服务是否正常运行
   - 确认网络连接稳定

3. **视觉识别失败**
   - 检查豆包API配置
   - 确认`eyes_doubaoreal`函数正常

4. **TTS播放异常**
   - 检查TTS服务配置
   - 确认音频输出正常

### 调试日志
```python
# 启用详细日志
import logging
logging.getLogger("plugins_func.functions.continuous_vision").setLevel(logging.DEBUG)
```

## 性能优化

### 1. 资源管理
- 自动清理过期的视觉会话
- 限制并发处理数量
- 优化图片处理流程

### 2. 网络优化
- 图片压缩处理
- 断线重连机制
- 超时重试策略

### 3. 内存优化
- 及时删除临时图片文件
- 控制会话数量
- 异步处理避免阻塞

## 扩展开发

### 添加新模式
```python
# 在VisionSession中添加新模式
def __init__(self, conn, mode, question):
    # mode: 1=识图, 2=指路, 3=新模式
    if mode == 3:
        self.question = "新模式的默认问题"
```

### 自定义处理逻辑
```python
# 重写图片处理方法
def _process_image_custom(self, image_path):
    # 自定义图片处理逻辑
    pass
```

## 与现有功能的兼容性

### HTTP服务集成
- 完全兼容现有的`http_server_service.py`
- 复用现有的图片上传处理逻辑
- 无需修改ESP32端代码

### 视觉识别集成
- 直接调用现有的`eyes_doubaoreal`函数
- 支持豆包视觉模型
- 保持现有的配置和API密钥

### TTS集成
- 无缝集成现有TTS流程
- 支持播放完成通知
- 保持现有的音频处理逻辑

## 总结

这个连续视觉识别功能：

1. **简洁设计**：只用服务端插件，无客户端IoT重复
2. **完整集成**：基于您现有的HTTP服务和视觉识别
3. **智能循环**：真正实现"播报完一段后播报下一段"
4. **易于使用**：通过语音命令控制，用户体验友好
5. **高度兼容**：不破坏现有功能，最小化修改

用户只需说"启用识图模式"，就能享受连续的视觉识别服务！
