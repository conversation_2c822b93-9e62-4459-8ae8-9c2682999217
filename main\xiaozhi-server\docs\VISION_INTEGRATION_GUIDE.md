# 豆包视觉集成指南

## 概述

基于您提到的aiglass项目实现，从API端动态获取豆包视觉大模型配置，客户端控制连续视觉逻辑。连通性测试只测试豆包视觉API是否可用，与设备ID无关。

## 架构设计

### 服务端职责
- 从API端动态获取豆包视觉配置
- 处理图片识别请求
- 调用豆包视觉大模型
- 返回识别结果并进行TTS播放

### 客户端职责
- 控制连续视觉的启动/停止
- 管理拍照时机和循环逻辑
- 监听TTS完成事件
- 触发下一次拍照

## 文件结构

```
xiaozhi-esp32-server/
├── main/xiaozhi-server/
│   ├── plugins_func/functions/
│   │   └── isee_eyes_doubao.py          # 豆包视觉API（新增）
│   ├── config_vision_example.yaml       # 配置示例（新增）
│   └── docs/
│       └── VISION_INTEGRATION_GUIDE.md  # 本文档（新增）
└── main/iot/things/
    └── mode_controller.cc               # 客户端控制逻辑（已修改）
```

## 配置说明

### 1. API端配置豆包视觉

豆包视觉配置通过API端动态获取，需要在管理后台配置：

```json
{
  "Vision": {
    "DoubaoVision": {
      "api_key": "你的豆包视觉API密钥",
      "base_url": "https://ark.cn-beijing.volces.com/api/v3",
      "model": "ep-20241230140547-8xqzr",
      "max_tokens": 1000,
      "temperature": 0.7
    }
  }
}
```

### 2. 本地备用配置（可选）

如果API端配置不可用，可以在 `config.yaml` 中添加备用配置：

```yaml
# 视觉识别配置（备用）
Vision:
  DoubaoVision:
    api_key: "你的豆包视觉API密钥"
    base_url: "https://ark.cn-beijing.volces.com/api/v3"
    model: "ep-20241230140547-8xqzr"
    max_tokens: 1000
    temperature: 0.7
```

### 3. 配置ESP32摄像头

确保ESP32摄像头的`Explain`方法指向正确的服务端接口。

## 技术实现

### 服务端：豆包视觉API

#### DoubaoVisionProvider类
```python
class DoubaoVisionProvider:
    """豆包视觉大模型提供者"""

    def __init__(self, conn):
        """从API端动态获取豆包视觉配置"""

    def _load_config_from_api(self):
        """从API端获取豆包视觉配置"""

    def _load_local_config(self):
        """加载本地配置作为备用"""

    async def analyze_image(self, image_path, question):
        """分析图片并返回结果"""

    async def _call_doubao_vision_api(self, image_base64, question):
        """调用豆包视觉API"""
```

#### 核心函数
1. `eyes_doubaoreal(conn, mode, image_path)` - 主要的视觉识别函数
2. `capture_and_analyze_image(conn, question)` - 注册的拍照识图函数
3. `get_doubao_vision_provider(conn)` - 获取视觉提供者实例

### 客户端：连续视觉控制

#### ModeController修改
- 在`CaptureAndExplainImage`方法中添加连续捕获逻辑
- 通过`waiting_for_tts_complete_`状态管理循环
- 监听TTS完成事件触发下一次拍照

#### 工作流程
```
1. 用户通过IoT命令启用识图模式
2. ESP32立即拍第一张照片
3. 调用Camera::Explain发送图片到服务端
4. 服务端调用豆包视觉API分析图片
5. 返回识别结果并进行TTS播放
6. TTS播放完成后通知ESP32
7. ESP32收到通知，自动拍下一张照片
8. 重复步骤3-7，实现连续循环
```

## 使用方式

### 语音命令（通过现有插件）
- "拍照识图" - 单次拍照识别

### IoT命令（通过ModeController）
```json
// 启用识图模式
{
  "method": "switch_to_image_mode",
  "parameters": {
    "auto_capture": true,
    "question": "请描述这张图片"
  }
}

// 启用指路模式
{
  "method": "switch_to_navigation_mode",
  "parameters": {
    "auto_capture": true
  }
}

// 停止连续模式
{
  "method": "switch_to_chat_mode",
  "parameters": {}
}
```

## 使用示例

### 场景1：连续识图监控
```
服务端发送IoT命令启用识图模式
↓
ESP32立即拍第一张照片
↓
图片发送到服务端豆包视觉API
↓
豆包分析："我看到一个桌子上放着..."
↓
TTS播报识别结果
↓
TTS播报完成，通知ESP32
↓
ESP32自动拍下一张照片
↓
继续循环...
```

### 场景2：连续导航
```
服务端发送IoT命令启用指路模式
↓
ESP32立即拍第一张照片
↓
图片发送到服务端豆包视觉API
↓
豆包分析："前方是十字路口，建议..."
↓
TTS播报导航信息
↓
TTS播报完成，通知ESP32
↓
ESP32自动拍下一张照片
↓
继续循环...
```

## API调用示例

### 豆包视觉API请求格式
```python
payload = {
    "model": "ep-20241230140547-8xqzr",
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "请描述这张图片"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{image_base64}"
                    }
                }
            ]
        }
    ],
    "max_tokens": 1000,
    "temperature": 0.7
}
```

### 响应格式
```json
{
    "choices": [
        {
            "message": {
                "content": "我看到一张图片，显示了..."
            }
        }
    ]
}
```

## 集成步骤

### 1. 服务端集成
1. 将 `isee_eyes_doubao.py` 放到 `plugins_func/functions/` 目录
2. 在管理后台API端配置豆包视觉参数
3. 重启xiaozhi-server服务（模块会自动进行连通性测试）

### 2. 客户端集成
1. ModeController代码已经修改完成
2. 确保ESP32摄像头的Explain方法配置正确
3. 重新编译并烧录ESP32固件

### 3. 测试验证

#### 启动日志示例
```
2024-01-01 10:00:00 | INFO | 🚀 豆包视觉模块加载 - 开始连通性检测...
2024-01-01 10:00:01 | INFO | 从API获取豆包视觉配置成功，模型: ep-20241230140547-8xqzr
2024-01-01 10:00:02 | INFO | 豆包视觉API连通性测试成功: 连接正常
2024-01-01 10:00:02 | INFO | ✅ 豆包视觉模块连通性测试通过
```

#### 功能测试
1. 通过语音命令测试单次拍照："拍照识图"
2. 通过IoT命令测试连续模式
3. 验证TTS完成后的自动循环功能

## 错误处理

### 常见问题

#### 1. 启动连通性测试失败
```
🚀 豆包视觉模块加载 - 开始连通性检测...
从API获取豆包视觉配置成功，模型: ep-20241230140547-8xqzr
豆包视觉API连通性测试失败: 401, {"error": "Invalid API key"}
⚠️  豆包视觉模块连通性测试失败
```
**解决方案：**
- 检查API端配置中的豆包视觉API密钥是否正确
- 确认豆包视觉模型ID是否有效
- 验证网络连接是否正常

#### 2. 豆包API调用失败
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看API配额是否充足

#### 3. 图片上传失败
   - 检查ESP32摄像头是否正常
   - 确认图片文件路径正确

#### 4. 连续模式不循环
   - 检查TTS完成通知是否正常
   - 确认ModeController状态管理正确

#### 5. 配置获取失败
   - 检查API端是否正确配置豆包视觉参数
   - 确认设备ID和客户端ID是否正确
   - 验证本地备用配置是否完整

### 调试方法
```python
# 启用详细日志
import logging
logging.getLogger("plugins_func.functions.isee_eyes_doubao").setLevel(logging.DEBUG)
```

## 扩展开发

### 添加其他视觉AI服务
```python
class OpenAIVisionProvider:
    """OpenAI视觉提供者"""

    async def analyze_image(self, image_path, question):
        # 实现OpenAI视觉API调用
        pass
```

### 自定义识别问题
```python
# 在调用时传入自定义问题
result = await vision_provider.analyze_image(image_path, "请分析这张图片中的安全隐患")
```

## 优势

1. **职责分离**：服务端负责AI处理，客户端负责控制逻辑
2. **基于现有架构**：复用ESP32的Camera::Explain方法
3. **智能循环**：真正的播报完成后自动继续
4. **易于配置**：只需配置豆包API密钥
5. **可扩展**：支持添加其他视觉AI服务

## 总结

这个方案完全按照您的要求实现：

1. **API端配置获取**：参考aiglass项目，从API端动态获取豆包视觉大模型配置
2. **客户端控制**：连续视觉逻辑在ESP32端控制，智能等待TTS完成
3. **自动测试**：模块加载时自动进行连通性测试，无需额外配置
4. **简洁架构**：一个文件完成所有功能，职责分离，代码清晰
5. **完整功能**：支持单次拍照和连续模式

用户可以通过语音命令或IoT命令启用连续视觉识别，享受基于豆包视觉大模型的智能识别服务！
