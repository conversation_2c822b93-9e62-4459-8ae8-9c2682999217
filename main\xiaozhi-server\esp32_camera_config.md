# ESP32摄像头配置指南

## 问题分析

根据错误信息和代码分析，发现以下问题：

1. **服务端启动错误**：`register_function`参数格式不正确
2. **ESP32摄像头未配置**：需要设置`SetExplainUrl`指向视觉HTTP服务器
3. **缺少视觉HTTP接口**：服务端需要接收ESP32上传的图片

## 解决方案

### 1. 服务端修复

#### 已修复的问题：
- ✅ 修复了`register_function`参数格式错误
- ✅ 创建了独立的视觉HTTP服务器 (`vision_http_server.py`)
- ✅ 实现了豆包视觉API的连通性测试

#### 启动视觉HTTP服务器：

在主应用程序中添加：

```python
# 在 app.py 中添加
from vision_http_server import start_vision_http_server

async def main():
    check_ffmpeg_installed()
    config = load_config()
    
    # 启动视觉HTTP服务器
    await start_vision_http_server(config)
    
    # 继续其他启动流程...
```

### 2. ESP32摄像头配置

#### 方法1：通过MCP协议配置（推荐）

ESP32支持通过MCP协议动态配置摄像头URL：

```json
{
  "method": "initialize",
  "params": {
    "capabilities": {
      "vision": {
        "url": "http://服务器IP:8003/xiaozhi/vision/explain",
        "token": ""
      }
    }
  }
}
```

#### 方法2：通过代码配置

在ESP32初始化代码中添加：

```cpp
// 在board初始化时配置摄像头
auto camera = board.GetCamera();
if (camera) {
    camera->SetExplainUrl("http://服务器IP:8003/xiaozhi/vision/explain", "");
}
```

#### 方法3：通过配置文件

在ESP32的配置中添加：

```cpp
// 在settings中配置
Settings settings("camera", true);
settings.SetString("explain_url", "http://服务器IP:8003/xiaozhi/vision/explain");
settings.SetString("explain_token", "");

// 在初始化时读取
auto camera = board.GetCamera();
if (camera) {
    Settings settings("camera", false);
    std::string url = settings.GetString("explain_url");
    std::string token = settings.GetString("explain_token");
    if (!url.empty()) {
        camera->SetExplainUrl(url, token);
    }
}
```

### 3. 配置文件更新

#### 服务端配置 (config.yaml)

```yaml
# 服务器配置
server:
  ip: "0.0.0.0"
  port: 8000
  ota_port: 8002
  vision_port: 8003  # 新增：视觉HTTP服务器端口

# 豆包视觉配置（备用，优先从API获取）
Vision:
  DoubaoVision:
    api_key: "你的豆包视觉API密钥"
    base_url: "https://ark.cn-beijing.volces.com/api/v3"
    model: "ep-20241230140547-8xqzr"
    max_tokens: 1000
    temperature: 0.7
```

#### API端配置

在管理后台配置豆包视觉参数：

```json
{
  "Vision": {
    "DoubaoVision": {
      "api_key": "你的豆包视觉API密钥",
      "base_url": "https://ark.cn-beijing.volces.com/api/v3",
      "model": "ep-20241230140547-8xqzr",
      "max_tokens": 1000,
      "temperature": 0.7
    }
  }
}
```

## 完整工作流程

### 1. 启动流程

```
1. 启动xiaozhi-server服务
   ├── 加载豆包视觉模块 (isee_eyes_doubao.py)
   ├── 自动进行连通性测试
   ├── 启动视觉HTTP服务器 (端口8003)
   └── 启动WebSocket服务器 (端口8000)

2. ESP32设备连接
   ├── 连接到WebSocket服务器
   ├── 通过MCP协议配置摄像头URL
   └── 准备接收IoT命令
```

### 2. 连续视觉识别流程

```
1. 用户语音命令或IoT命令启用连续模式
   ↓
2. ESP32切换到识图/导航模式
   ↓
3. ESP32立即拍第一张照片 (Camera::Capture)
   ↓
4. ESP32调用Camera::Explain发送图片到视觉HTTP服务器
   ↓
5. 视觉HTTP服务器调用豆包视觉API分析图片
   ↓
6. 返回识别结果给ESP32
   ↓
7. ESP32通过WebSocket发送结果给服务端
   ↓
8. 服务端进行TTS播报
   ↓
9. TTS播报完成，通知ESP32
   ↓
10. ESP32自动拍下一张照片，回到步骤4
```

## 测试验证

### 1. 服务端测试

```bash
# 启动服务器
cd main/xiaozhi-server
python app.py

# 查看日志输出
🚀 豆包视觉模块加载 - 开始连通性检测...
从API获取豆包视觉配置成功，模型: ep-20241230140547-8xqzr
豆包视觉API连通性测试成功: 连接正常
✅ 豆包视觉模块连通性测试通过
视觉HTTP服务器已启动，端口: 8003
```

### 2. HTTP接口测试

```bash
# 健康检查
curl http://localhost:8003/xiaozhi/vision/health

# 图片上传测试
curl -X POST http://localhost:8003/xiaozhi/vision/explain \
  -H "Device-ID: test_device" \
  -H "Session-ID: test_session" \
  -F "question=请描述这张图片" \
  -F "file=@test_image.jpg"
```

### 3. ESP32测试

```cpp
// 在ESP32代码中测试
auto camera = Board::GetInstance().GetCamera();
if (camera) {
    // 设置URL
    camera->SetExplainUrl("http://*************:8003/xiaozhi/vision/explain", "");
    
    // 拍照测试
    if (camera->Capture()) {
        std::string result = camera->Explain("请描述这张图片");
        ESP_LOGI(TAG, "Vision result: %s", result.c_str());
    }
}
```

## 故障排除

### 常见问题

1. **服务端启动失败**
   - 检查端口8003是否被占用
   - 确认豆包视觉API配置正确

2. **ESP32连接失败**
   - 检查网络连接
   - 确认服务器IP地址正确
   - 验证防火墙设置

3. **图片识别失败**
   - 检查豆包视觉API密钥
   - 确认网络连接正常
   - 查看服务端日志

4. **连续模式不工作**
   - 确认ESP32摄像头URL已配置
   - 检查TTS完成通知是否正常
   - 验证ModeController状态

### 调试方法

```python
# 启用详细日志
import logging
logging.getLogger("plugins_func.functions.isee_eyes_doubao").setLevel(logging.DEBUG)
logging.getLogger("vision_http_server").setLevel(logging.DEBUG)
```

## 总结

通过以上配置，可以实现：

1. ✅ 服务端豆包视觉API集成
2. ✅ ESP32摄像头与服务端的通信
3. ✅ 连续视觉识别功能
4. ✅ 智能的TTS完成后自动循环

关键是确保ESP32摄像头的`SetExplainUrl`正确配置指向视觉HTTP服务器的地址。
