"""
连续视觉识别插件
基于现有的HTTP服务和eyes_doubaoreal函数实现连续识图和指路功能
"""

import asyncio
import time
import os
from plugins_func.register import register_function, Action, ActionResponse, ToolType
from core.handle.iotHandle import send_iot_conn
from loguru import logger

# 导入当前项目中的eyes_doubaoreal函数
try:
    from plugins_func.functions.eyes_doubao import eyes_doubaoreal
    EYES_DOUBAO_AVAILABLE = True
except ImportError:
    EYES_DOUBAO_AVAILABLE = False
    logger.warning("eyes_doubao模块不可用，将使用备用视觉处理")

TAG = __name__

# 全局状态管理
vision_sessions = {}  # 存储每个连接的视觉会话状态

class VisionSession:
    """视觉会话管理类"""

    def __init__(self, conn, mode, question="请描述这张图片"):
        self.conn = conn
        self.mode = mode  # 1=识图模式, 2=指路模式
        self.question = question
        self.is_active = False
        self.is_waiting_tts = False
        self.session_id = getattr(conn, 'session_id', 'default')

    def start(self):
        """启动连续视觉识别"""
        self.is_active = True
        self.is_waiting_tts = False
        logger.bind(tag=TAG).info(f"启动连续视觉识别，模式: {self.mode}, 问题: {self.question}")

        # 立即拍第一张照片
        self._trigger_capture()

    def stop(self):
        """停止连续视觉识别"""
        self.is_active = False
        self.is_waiting_tts = False
        logger.bind(tag=TAG).info(f"停止连续视觉识别")

    def on_tts_complete(self):
        """TTS播放完成回调"""
        if self.is_active and self.is_waiting_tts:
            self.is_waiting_tts = False
            logger.bind(tag=TAG).info(f"TTS播放完成，触发下一次拍照")
            # 延迟一点再拍下一张，避免过于频繁
            asyncio.create_task(self._delayed_capture())

    async def _delayed_capture(self):
        """延迟拍照"""
        await asyncio.sleep(1)  # 延迟1秒
        if self.is_active and not self.is_waiting_tts:
            self._trigger_capture()

    def _trigger_capture(self):
        """触发拍照"""
        if not self.is_active:
            return

        logger.bind(tag=TAG).info(f"触发拍照，模式: {self.mode}")

        # 使用现有的IoT命令拍照
        try:
            if hasattr(self.conn, 'loop') and self.conn.loop:
                # 在conn的事件循环中运行
                asyncio.run_coroutine_threadsafe(
                    send_iot_conn(self.conn, "Camera", "take_photo", {}),
                    self.conn.loop
                )
            else:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(send_iot_conn(self.conn, "Camera", "take_photo", {}))

            # 设置等待状态，等待图片上传和处理
            self._wait_for_image_processing()

        except Exception as e:
            logger.bind(tag=TAG).error(f"拍照失败: {e}")

    def _wait_for_image_processing(self):
        """等待图片处理"""
        # 在后台线程中等待图片上传和处理
        def process_in_background():
            try:
                # 等待图片上传
                image_path = f"tmp/iot_img_{self.session_id}.jpg"
                start_time = time.time()

                # 等待图片文件出现
                while not os.path.exists(image_path) and time.time() - start_time < 10:
                    time.sleep(0.2)

                if not os.path.exists(image_path):
                    logger.bind(tag=TAG).error(f"等待图片超时: {image_path}")
                    return

                # 等待文件写入完成
                file_size = 0
                for _ in range(10):
                    file_size = os.path.getsize(image_path)
                    if file_size > 0:
                        break
                    time.sleep(0.5)

                if file_size == 0:
                    logger.bind(tag=TAG).error(f"图片文件大小为0: {image_path}")
                    return

                logger.bind(tag=TAG).info(f"图片上传完成，开始处理: {image_path}, 大小: {file_size}")

                # 调用现有的eyes_doubaoreal函数处理图片
                if EYES_DOUBAO_AVAILABLE:
                    result = eyes_doubaoreal(self.conn, self.mode, image_path)
                else:
                    # 备用处理：使用现有的eyes_iot功能
                    try:
                        from plugins_func.functions.eyes_iot import iot_vision_function
                        result = iot_vision_function(self.conn)
                    except ImportError:
                        logger.bind(tag=TAG).error("没有可用的视觉处理功能")
                        result = ActionResponse(
                            action=Action.RESPONSE,
                            result=None,
                            response="抱歉，视觉功能暂时不可用"
                        )

                if result and hasattr(result, 'response') and result.response:
                    # 设置等待TTS状态
                    self.is_waiting_tts = True

                    # 通过TTS播放结果
                    if hasattr(self.conn, 'tts') and self.conn.tts:
                        from core.providers.tts.dto.dto import ContentType
                        self.conn.tts.tts_one_sentence(self.conn, ContentType.TEXT, content_detail=result.response)
                        logger.bind(tag=TAG).info(f"开始TTS播放: {result.response[:50]}...")
                    else:
                        logger.bind(tag=TAG).warning("TTS服务不可用")
                        self.is_waiting_tts = False
                else:
                    logger.bind(tag=TAG).error(f"图片处理失败或结果为空")

            except Exception as e:
                logger.bind(tag=TAG).error(f"后台处理图片时出错: {e}")

        # 在线程池中执行
        if hasattr(self.conn, 'executor') and self.conn.executor:
            self.conn.executor.submit(process_in_background)
        else:
            import threading
            threading.Thread(target=process_in_background, daemon=True).start()


def get_vision_session(conn):
    """获取或创建视觉会话"""
    session_id = getattr(conn, 'session_id', 'default')
    return vision_sessions.get(session_id)


def set_vision_session(conn, session):
    """设置视觉会话"""
    session_id = getattr(conn, 'session_id', 'default')
    if session:
        vision_sessions[session_id] = session
    else:
        vision_sessions.pop(session_id, None)


@register_function(
    name="启用识图模式",
    description="启用连续识图模式，设备会不断拍照识别并播报结果",
    parameters=[
        {
            "name": "question",
            "type": "string",
            "description": "对图片询问的问题，默认为'请描述这张图片'",
            "required": False
        }
    ],
    tool_type=ToolType.IOT_CTL
)
def start_image_recognition_mode(conn, question="请描述这张图片"):
    """启用识图模式"""
    try:
        # 停止现有会话
        existing_session = get_vision_session(conn)
        if existing_session:
            existing_session.stop()

        # 创建新的识图会话
        session = VisionSession(conn, mode=1, question=question)
        set_vision_session(conn, session)
        session.start()

        logger.bind(tag=TAG).info(f"识图模式已启用，问题: {question}")
        return ActionResponse(
            action=Action.RESPONSE,
            result="success",
            response=f"已启用识图模式，将连续拍照识别并询问：{question}"
        )

    except Exception as e:
        logger.bind(tag=TAG).error(f"启用识图模式失败: {e}")
        return ActionResponse(
            action=Action.ERROR,
            result=str(e),
            response="启用识图模式失败，请稍后重试"
        )


@register_function(
    name="启用指路模式",
    description="启用连续指路模式，设备会不断拍照分析路况并播报导航信息",
    parameters=[],
    tool_type=ToolType.IOT_CTL
)
def start_navigation_mode(conn):
    """启用指路模式"""
    try:
        # 停止现有会话
        existing_session = get_vision_session(conn)
        if existing_session:
            existing_session.stop()

        # 创建新的指路会话
        question = "请描述前方的路况和导航信息"
        session = VisionSession(conn, mode=2, question=question)
        set_vision_session(conn, session)
        session.start()

        logger.bind(tag=TAG).info(f"指路模式已启用")
        return ActionResponse(
            action=Action.RESPONSE,
            result="success",
            response="已启用指路模式，将连续分析前方路况并提供导航建议"
        )

    except Exception as e:
        logger.bind(tag=TAG).error(f"启用指路模式失败: {e}")
        return ActionResponse(
            action=Action.ERROR,
            result=str(e),
            response="启用指路模式失败，请稍后重试"
        )


@register_function(
    name="退出视觉模式",
    description="退出当前的识图或指路模式，回到正常对话模式",
    parameters=[],
    tool_type=ToolType.IOT_CTL
)
def stop_vision_mode(conn):
    """退出视觉模式"""
    try:
        session = get_vision_session(conn)
        if session:
            session.stop()
            set_vision_session(conn, None)
            logger.bind(tag=TAG).info(f"已退出视觉模式")
            return ActionResponse(
                action=Action.RESPONSE,
                result="success",
                response="已退出视觉模式，回到正常对话模式"
            )
        else:
            return ActionResponse(
                action=Action.RESPONSE,
                result="not_active",
                response="当前没有活跃的视觉模式"
            )

    except Exception as e:
        logger.bind(tag=TAG).error(f"退出视觉模式失败: {e}")
        return ActionResponse(
            action=Action.ERROR,
            result=str(e),
            response="退出视觉模式失败"
        )


@register_function(
    name="拍照识图",
    description="立即拍一张照片并进行识别，不启用连续模式",
    parameters=[
        {
            "name": "question",
            "type": "string",
            "description": "对图片询问的问题",
            "required": False
        }
    ],
    tool_type=ToolType.IOT_CTL
)
def capture_and_analyze(conn, question="请描述这张图片"):
    """单次拍照识图"""
    try:
        # 创建临时会话进行单次拍照
        temp_session = VisionSession(conn, mode=1, question=question)
        temp_session.is_active = True
        temp_session._trigger_capture()

        logger.bind(tag=TAG).info(f"单次拍照识图已触发，问题: {question}")
        return ActionResponse(
            action=Action.RESPONSE,
            result="success",
            response=f"正在拍照识图，问题：{question}"
        )

    except Exception as e:
        logger.bind(tag=TAG).error(f"单次拍照识图失败: {e}")
        return ActionResponse(
            action=Action.ERROR,
            result=str(e),
            response="拍照识图失败，请稍后重试"
        )


# TTS完成通知处理
def notify_tts_complete(conn):
    """通知TTS播放完成"""
    session = get_vision_session(conn)
    if session:
        session.on_tts_complete()
