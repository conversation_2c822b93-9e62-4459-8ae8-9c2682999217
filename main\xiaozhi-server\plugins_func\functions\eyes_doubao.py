from plugins_func.register import register_function, ToolType, ActionResponse, Action
import requests
from volcenginesdkarkruntime import Ark
import base64
from config.logger import setup_logging

from datetime import datetime
from core.handle.iotHandle import send_iot_conn
from core.handle.sendAudioHandle import sendAudioMessage
import asyncio
import threading
import time
import os

TAG = __name__
logger = setup_logging()

eyes_function_desc = {
    "type": "function",
    "function": {
        "name": "eyes_doubao",
        "description": "小智的眼睛, 可以识别眼前的东西,用户要问前面或者看到了什么之类的关键词的时候直接调用即可",
        'parameters': {'type': 'object', 'properties': {}, 'required': []}
    }
}

# 图片转base64函数


def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


@register_function('eyes_doubao', eyes_function_desc, ToolType.IOT_CTL)
def eyes_doubao(conn):
    logger.bind(tag=TAG).info("开始调用iot拍照并上传到服务器")

    # 使用session_id作为图片文件名的一部分，避免冲突
    image_path = f"tmp/iot_img_{conn.session_id}.jpg" if conn and hasattr(
        conn, 'session_id') else "tmp/image.jpg"

    # 删除临时图片
    if os.path.exists(image_path):
        os.remove(image_path)
    use_pic = False
    # 处理图片上传
    try:
        # if conn is not None:
        #     url = f"http://{conn.client_ip}/jpg"
        # else:
        #     url = 'http://***************/jpg'  # 改成你自己的板子ip
        # # 发送请求
        # response = requests.get(url, timeout=1000)
        # use_pic = False
        # if response.status_code == 200:
        #     with open('tmp/image.jpg', 'wb') as file:
        #         file.write(response.content)
        #         print('文件下载成功')
        #         use_pic = True
        # else:
        #     print('文件下载失败')
        logger.bind(tag=TAG).info("begin to take photo")
        asyncio.run(send_iot_conn(conn, "Camera", "take_photo", {}))
        # 这里可以添加代码来处理图片上传
        logger.bind(tag=TAG).info("begin to upload photo")
        # 开启服务器, 获取图片
        # 2. 等待并检查图片上传（最多等待8秒）
        logger.bind(tag=TAG).info("等待图片上传...")
        start_time = time.time()
        while not os.path.exists(image_path):
            time.sleep(0.2)
            if time.time() - start_time > 8:
                logger.bind(tag=TAG).error("等待图片超时")
                break
        logger.bind(tag=TAG).info("等待图片上传完毕")

        # 3. 检查结果
        if os.path.exists(image_path):
            use_pic = True
            logger.bind(tag=TAG).info("图片上传验证成功")
        else:
            logger.bind(tag=TAG).error("未检测到上传的图片")
    except Exception as e:
        logger.bind(tag=TAG).error(
            f"Error in response generation: {e}")
        return "【图片服务响应异常】"

    # # 删除可能存在的旧图片
    # if os.path.exists(image_path):
    #     try:
    #         os.remove(image_path)
    #         logger.bind(tag=TAG).info(f"已删除旧图片: {image_path}")
    #     except Exception as e:
    #         logger.bind(tag=TAG).warning(f"删除旧图片失败: {str(e)}")

    start_time = time.time()

    if use_pic == False:
        return "【图片服务响应异常】"

    # # 使用run_coroutine_threadsafe代替asyncio.run
    # if conn and hasattr(conn, 'loop'):
    #     try:
    #         # 使用conn对象中的事件循环
    #         loop = conn.loop
    #         # 在conn的事件循环中运行异步函数
    #         future = asyncio.run_coroutine_threadsafe(
    #             send_iot_conn(conn, "Camera", "take_photo", {}),
    #             loop
    #         )
    #         # 等待结果返回，设置超时时间
    #         photo_result = future.result(timeout=10)
    #         logger.bind(tag=TAG).info(f"已发送拍照命令，结果: {photo_result}")

    #         # 等待图片上传完成（最多等待10秒）
    #         logger.bind(tag=TAG).info(f"等待图片上传到: {image_path}")
    #         wait_start_time = time.time()
    #         while not os.path.exists(image_path):
    #             time.sleep(0.2)
    #             if time.time() - wait_start_time > 10:
    #                 logger.bind(tag=TAG).warning("等待图片上传超时")
    #                 break

    #         # 检查图片是否存在且大小大于0
    #         if os.path.exists(image_path) and os.path.getsize(image_path) > 0:
    #             logger.bind(tag=TAG).info(
    #                 f"图片上传完成，文件大小: {os.path.getsize(image_path)} 字节")
    #         else:
    #             logger.bind(tag=TAG).warning(f"图片上传失败或文件大小为0")
    #     except Exception as e:
    #         logger.bind(tag=TAG).error(f"拍照时出错: {str(e)}")
    # else:
    #     logger.bind(tag=TAG).warning(f"连接对象为空或没有loop属性，无法拍照")

    # end_time = time.time()
    # logger.bind(tag=TAG).info(
    #     f"eyes_doubao上传图片iot函数处理完成，耗时: {end_time - start_time:.2f}秒")

    # 调用eyes_doubaoreal处理图片
    return eyes_doubaoreal(conn, 0, image_path)


def eyes_doubaoreal(conn=None, mode=0, image_path=""):
    # 使用固定的API密钥
    api_key = "d9350ce7-d11f-4fe1-a2f0-708e496161b0"

    # 也可以从配置中获取API密钥
    # api_key = conn.config["plugins"]["get_weather"]["api_key"]

    # 获取客户端IP地址
    # if conn is not None:
    #     url = f"http://{conn.client_ip}/jpg"
    #     # 记录客户端IP地址，便于调试
    #     logger.bind(tag=TAG).info(f"从连接对象获取到客户端IP: {conn.client_ip}")
    # else:
    #     url = 'http://***************/jpg'  # 改成你自己的板子ip

    # # 从客户端获取图像
    # response = requests.get(url, timeout=2000)

    # 根据模式设置不同的提示词
    prompt = "请描述一下这张图片"
    if mode == 2:  # 指路模式
        prompt = "请描述一下这张图片，并告诉我如何前往图中的目的地"
        logger.bind(tag=TAG).info(f"使用指路模式处理图片")
    elif mode == 1:  # 识图模式
        prompt = "请详细描述这张图片中的内容"
        logger.bind(tag=TAG).info(f"使用识图模式处理图片")

    # 如果有连接对象，设置提示词
    if conn:
        conn.prompt = prompt

    # # 保存图像到本地
    # if response.status_code == 200:
    #     with open('tmp/image.jpg', 'wb') as file:
    #         file.write(response.content)
    #         print('文件下载成功')
    # else:
    #     print('文件下载失败')
    logger.bind(tag=TAG).info("eye_doubao begin to take photo2")
    # print("eye_doubao begin to take photo2")

    # # 通知客户端上传图片，但不使用 asyncio.run()
    # if conn and hasattr(conn, 'loop'):
    #     # 如果连接对象有事件循环，使用它来运行异步函数
    #     future = asyncio.run_coroutine_threadsafe(
    #         send_iot_conn(conn, "Camera", "take_photo", {}),
    #         conn.loop
    #     )
    #     try:
    #         # 等待异步函数完成
    #         future.result(timeout=5)
    #         logger.bind(tag=TAG).info("已通知客户端拍照并上传图片")
    #     except Exception as e:
    #         logger.bind(tag=TAG).error(f"通知客户端拍照时出错: {str(e)}")
    # else:
    #     logger.bind(tag=TAG).warning("无法通知客户端拍照，将使用已上传的图片")

    # print("eye_doubao begin to wait for photo upload")
    # 开启服务器, 获取图片
    # if mode == -1:#对话模式都是llm调用所以要在这里调用iot上传图片，其他模式是主动调用所以不需要再上传一遍
    #     asyncio.run(send_iot_conn(conn, "Camera", "take_photo", {}))
    #     return ActionResponse(Action.ERROR, None, "未检测到图片文件")

    # 使用session_id作为图片文件名的一部分，避免冲突
    # image_path = f"tmp/iot_img_{conn.session_id}.jpg" if conn and hasattr(
    #     conn, 'session_id') else "tmp/image.jpg"

    # 2. 检查图片是否存在
    logger.bind(tag=TAG).info(f"检查图片是否存在: {image_path}")
    if os.path.exists(image_path) and os.path.getsize(image_path) > 0:
        logger.bind(tag=TAG).info(f"图片存在，准备进行处理: {image_path}")
    else:
        logger.bind(tag=TAG).error(
            f"未检测到图片文件: {image_path} ")
        return ActionResponse(Action.ERROR, None, "未检测到图片文件")

    # 初始化Ark客户端
    client = Ark(
        # 此为默认路径，您可根据业务所在地域进行配置
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        # 使用API密钥
        api_key=api_key,
    )

    # 原图片转base64
    base64_image = encode_image(image_path)

    # 获取提示词（如果连接对象中有自定义提示词，则使用它）
    prompt = "描述一下这个图片"
    if conn and hasattr(conn, 'prompt') and conn.prompt:
        prompt = conn.prompt
        logger.bind(tag=TAG).info(f"使用自定义提示词: {prompt}")
    else:
        logger.bind(tag=TAG).info(f"使用默认提示词: {prompt}")

    # 调用豆包大模型进行图像识别
    try:
        logger.bind(tag=TAG).info(f"开始调用豆包大模型，API密钥: {api_key[:8]}...")
        response = client.chat.completions.create(
            # 指定您创建的方舟推理接入点 ID
            model="doubao-1-5-vision-pro-32k-250115",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            },
                        },
                    ],
                }
            ],
        )
        logger.bind(tag=TAG).info(f"豆包大模型调用成功")
    except Exception as e:
        logger.bind(tag=TAG).error(f"调用豆包大模型时出错: {str(e)}")
        return ActionResponse(Action.ERROR, None, f"图像识别失败: {str(e)}")

    # 获取识别结果
    result_text = response.choices[0].message.content
    print(result_text)

    # 直接返回识别结果，让系统自动处理
    # 不要调用conn.chat()，因为它会导致参数不匹配的错误
    logger.bind(tag=TAG).info(f"返回识别结果，让系统自动处理")
    return ActionResponse(Action.RESPONSE, None, result_text)
