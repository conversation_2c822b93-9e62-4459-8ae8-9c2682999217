"""
IoT 视觉功能插件

通过 IoT 功能调用客户端的 TakePhoto 方法，然后使用视觉模型分析图像
"""

import os
import time
import base64
import json
import re
from typing import Optional, Dict, Any, List

# 导入必要的模块
from config.logger import setup_logging
from config.settings import load_config
from plugins_func.register import register_function, ToolType, ActionResponse, Action

# 尝试导入可选的依赖
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

try:
    from volcenginesdkarkruntime import Ark
    VOLC_SDK_AVAILABLE = True
except ImportError:
    VOLC_SDK_AVAILABLE = False

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# 设置日志
TAG = "eyes_iot"
logger = setup_logging()

def mask_api_key(api_key):
    """安全地掩盖 API 密钥，只显示前4位和后4位"""
    if not api_key or len(api_key) < 8:
        return "无效密钥"
    return f"{api_key[:4]}...{api_key[-4:]}"

# 定义函数描述
eyes_iot_function_desc = {
    "type": "function",
    "function": {
        "name": "eyes_iot",
        "description": "小智的眼睛, 通过IoT功能拍照并识别小智眼前的东西",
        'parameters': {'type': 'object', 'properties': {}, 'required': []}
    }
}

def encode_image(image_path):
    """将图片文件转换为base64编码

    Args:
        image_path: 图片文件路径

    Returns:
        str: base64编码的图片数据
    """
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def process_image(image_path):
    """处理图像（如果OpenCV可用）

    Args:
        image_path: 原始图像路径

    Returns:
        str: 处理后的图像路径
    """
    if not CV2_AVAILABLE:
        return image_path

    try:
        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            logger.bind(tag=TAG).error(f"无法读取图像: {image_path}")
            return image_path

        # 水平翻转（镜像处理）
        flipped_img = cv2.flip(img, 1)

        # 保存处理后的图像
        processed_path = image_path.replace('.jpg', '_processed.jpg')
        cv2.imwrite(processed_path, flipped_img)
        logger.bind(tag=TAG).info(f"图像处理成功: {processed_path}")
        return processed_path

    except Exception as e:
        logger.bind(tag=TAG).error(f"图像处理失败: {str(e)}")
        return image_path

def wait_for_image(conn, timeout=10):
    """等待客户端发送图片数据

    Args:
        conn: 连接处理器实例
        timeout: 超时时间（秒）

    Returns:
        str: 图片路径，如果超时或图片无效则返回 None
    """
    # 这个函数的实现取决于客户端如何发送图片数据
    # 在实际实现中，您需要根据客户端的行为来调整这个函数

    # 获取用户ID（设备ID）
    user_id = getattr(conn, 'device_id', None)
    if not user_id:
        # 如果没有设备ID，尝试从headers中获取
        user_id = getattr(conn, 'headers', {}).get('device-id', 'unknown')

    # 替换无效字符（Windows文件名不能包含: / \ * ? " < > |）
    safe_user_id = user_id.replace(':', '_').replace('/', '_').replace('\\', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
    logger.bind(tag=TAG).info(f"原始用户ID: {user_id}, 安全用户ID: {safe_user_id}")

    # 使用固定的图片名称格式
    image_path = f'tmp/iot_img_{safe_user_id}.jpeg'
    logger.bind(tag=TAG).info(f"等待图像文件: {image_path}")

    start_time = time.time()

    # 如果文件已经存在，记录其修改时间
    old_mtime = 0
    if os.path.exists(image_path):
        old_mtime = os.path.getmtime(image_path)
        logger.bind(tag=TAG).info(f"发现已存在的图像文件: {image_path}，修改时间: {old_mtime}")

    while time.time() - start_time < timeout:
        # 检查文件是否存在且是新创建或修改的
        if os.path.exists(image_path):
            current_mtime = os.path.getmtime(image_path)
            if current_mtime > old_mtime:
                # 验证图像文件是否有效
                if validate_image(image_path):
                    logger.bind(tag=TAG).info(f"收到有效的图像文件: {image_path}，修改时间: {current_mtime}")
                    return image_path
                else:
                    logger.bind(tag=TAG).error(f"收到无效的图像文件: {image_path}")
                    # 如果图像无效，继续等待
                    old_mtime = current_mtime
        time.sleep(0.1)

    logger.bind(tag=TAG).error(f"等待图像超时，未收到有效图像")
    return None

def validate_image(image_path):
    """验证图像文件是否有效

    Args:
        image_path: 图像文件路径

    Returns:
        bool: 图像是否有效
    """
    try:
        # 检查文件大小
        file_size = os.path.getsize(image_path)
        if file_size < 100:  # 小于100字节的图像文件可能是无效的
            logger.bind(tag=TAG).error(f"图像文件过小: {file_size} 字节")
            return False

        # 如果OpenCV可用，尝试读取图像
        if CV2_AVAILABLE:
            img = cv2.imread(image_path)
            if img is None or img.size == 0:
                logger.bind(tag=TAG).error("OpenCV无法读取图像")
                return False

            # 检查图像尺寸
            height, width = img.shape[:2]
            if height < 10 or width < 10:  # 尺寸过小的图像可能是无效的
                logger.bind(tag=TAG).error(f"图像尺寸过小: {width}x{height}")
                return False

            logger.bind(tag=TAG).info(f"图像验证成功: {width}x{height}, {file_size} 字节")
            return True
        else:
            # 如果OpenCV不可用，只检查文件大小
            logger.bind(tag=TAG).info(f"OpenCV不可用，仅检查文件大小: {file_size} 字节")
            return file_size > 1000  # 大于1KB的文件可能是有效的

    except Exception as e:
        logger.bind(tag=TAG).error(f"图像验证失败: {str(e)}")
        return False

def use_volcano_engine(base64_image):
    """使用火山引擎进行图像分析

    Args:
        base64_image: base64编码的图像数据

    Returns:
        ActionResponse: 包含分析结果的响应对象
    """
    try:
        # 从配置文件获取API密钥
        config = load_config()
        api_key = config.get("volcano_engine", {}).get("api_key")
        base_url = "https://ark.cn-beijing.volces.com/api/v3"
        model = "doubao-1-5-vision-pro-32k-250115"

        # 记录配置状态（不记录敏感信息）
        logger.bind(tag=TAG).info(f"火山引擎配置状态:")
        logger.bind(tag=TAG).info(f"- API 密钥存在: {bool(api_key)}")
        logger.bind(tag=TAG).info(f"- API 密钥长度: {len(api_key) if api_key else 0}")
        if api_key:
            masked_key = mask_api_key(api_key)
            logger.bind(tag=TAG).debug(f"- API 密钥掩码: {masked_key}")
        logger.bind(tag=TAG).info(f"- 基础 URL: {base_url}")
        logger.bind(tag=TAG).info(f"- 模型名称: {model}")

        # 如果配置文件中没有API密钥，使用默认值
        if not api_key:
            logger.bind(tag=TAG).warning("未在配置文件中找到火山引擎API密钥，使用默认值")
            api_key = "d9350ce7-d11f-4fe1-a2f0-708e496161b0"  # 默认值，应该替换为实际的API密钥

        # 初始化Ark客户端
        client = Ark(
            base_url=base_url,
            api_key=api_key,
        )

        logger.bind(tag=TAG).info(f"调用火山引擎视觉模型: {model}")

        # 记录请求消息
        request_content = [
            {"type": "text", "text": "请详细描述这张图片中的内容..."},
            {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,[图像数据已省略]"}}
        ]
        logger.bind(tag=TAG).info(f"请求消息 --> {json.dumps(request_content, ensure_ascii=False)}")
        response = client.chat.completions.create(
            model=model,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "请详细描述这张图片中的内容。如果有人，请描述他们的外貌、表情和动作。如果有物体，请描述它们的外观和位置。"},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            },
                        },
                    ],
                }
            ],
        )

        analysis = response.choices[0].message.content
        logger.bind(tag=TAG).info(f"火山引擎视觉分析完成，结果长度: {len(analysis)}")

        # 记录响应消息（只记录前100个字符，避免日志过长）
        truncated_analysis = analysis[:100] + "..." if len(analysis) > 100 else analysis
        logger.bind(tag=TAG).info(f"响应消息 <-- {truncated_analysis}")

        return ActionResponse(
            action=Action.RESPONSE,
            result=None,
            response=analysis
        )

    except Exception as e:
        # 记录详细的错误信息，但不包括敏感数据
        error_type = type(e).__name__
        error_message = str(e)

        logger.bind(tag=TAG).error(f"火山引擎视觉分析失败: {error_type}: {error_message}")

        # 添加更多诊断信息
        if "401" in error_message or "认证" in error_message:
            logger.bind(tag=TAG).error("认证错误：API 密钥可能无效或缺失")
            if api_key:
                logger.bind(tag=TAG).debug(f"API 密钥前缀: {api_key[:4]}...")
            else:
                logger.bind(tag=TAG).error("API 密钥为空")
        elif "404" in error_message:
            logger.bind(tag=TAG).error("未找到错误 (404)：API 端点可能不正确")
        elif "429" in error_message:
            logger.bind(tag=TAG).error("请求过多错误 (429)：已超过 API 速率限制")
        elif "模型不存在" in error_message:
            logger.bind(tag=TAG).error(f"模型错误：指定的模型 '{model}' 可能不存在或不可用")

        # 如果火山引擎失败，尝试使用OpenAI
        if OPENAI_AVAILABLE:
            logger.bind(tag=TAG).info("尝试使用OpenAI作为备选")
            return use_openai(base64_image)
        else:
            return ActionResponse(
                action=Action.RESPONSE,
                result=None,
                response=f"抱歉，我的视觉功能暂时出现问题: {error_type}"
            )



def use_openai(base64_image):
    """使用OpenAI进行图像分析

    Args:
        base64_image: base64编码的图像数据

    Returns:
        ActionResponse: 包含分析结果的响应对象
    """
    try:
        # 从配置文件获取API密钥和模型
        config = load_config()
        llm_config = config.get("LLM", {}).get("ChatGLMLLM", {})
        api_key = llm_config.get("api_key")
        base_url = llm_config.get("base_url") or llm_config.get("url")
        model = llm_config.get("model_name") or "gpt-4-vision-preview"

        # 记录配置状态（不记录敏感信息）
        logger.bind(tag=TAG).info(f"OpenAI 配置状态:")
        logger.bind(tag=TAG).info(f"- API 密钥存在: {bool(api_key)}")
        logger.bind(tag=TAG).info(f"- API 密钥长度: {len(api_key) if api_key else 0}")
        if api_key:
            masked_key = mask_api_key(api_key)
            logger.bind(tag=TAG).debug(f"- API 密钥掩码: {masked_key}")
        logger.bind(tag=TAG).info(f"- 基础 URL 存在: {bool(base_url)}")
        if base_url:
            logger.bind(tag=TAG).debug(f"- 基础 URL 前缀: {base_url[:10]}...")
        logger.bind(tag=TAG).info(f"- 模型名称: {model}")

        # 检查 API 密钥格式
        if api_key and not api_key.startswith("sk-"):
            logger.bind(tag=TAG).warning("API 密钥格式可能不正确，OpenAI API 密钥通常以 'sk-' 开头")

        # 如果配置文件中没有API密钥，使用默认值
        if not api_key:
            logger.bind(tag=TAG).warning("未在配置文件中找到OpenAI API密钥，使用默认值")
            api_key = "sk-xxx"  # 默认值，应该替换为实际的API密钥

        # 创建OpenAI客户端
        client = OpenAI(api_key=api_key, base_url=base_url)

        logger.bind(tag=TAG).info(f"调用OpenAI视觉模型: {model}")

        # 记录请求消息
        request_content = [
            {"type": "text", "text": "请详细描述这张图片中的内容..."},
            {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,[图像数据已省略]"}}
        ]
        logger.bind(tag=TAG).info(f"请求消息 --> {json.dumps(request_content, ensure_ascii=False)}")
        response = client.chat.completions.create(
            model=model,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请详细描述这张图片中的内容。如果有人，请描述他们的外貌、表情和动作。如果有物体，请描述它们的外观和位置。"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=1000
        )

        analysis = response.choices[0].message.content
        logger.bind(tag=TAG).info(f"OpenAI视觉分析完成，结果长度: {len(analysis)}")

        # 记录响应消息（只记录前100个字符，避免日志过长）
        truncated_analysis = analysis[:100] + "..." if len(analysis) > 100 else analysis
        logger.bind(tag=TAG).info(f"响应消息 <-- {truncated_analysis}")

        return ActionResponse(
            action=Action.RESPONSE,
            result=None,
            response=analysis
        )

    except Exception as e:
        # 记录详细的错误信息，但不包括敏感数据
        error_type = type(e).__name__
        error_message = str(e)

        logger.bind(tag=TAG).error(f"OpenAI视觉分析失败: {error_type}: {error_message}")

        # 添加更多诊断信息
        if "401" in error_message:
            logger.bind(tag=TAG).error("认证错误 (401)：API 密钥可能无效或缺失")
            if api_key:
                logger.bind(tag=TAG).debug(f"API 密钥前缀: {api_key[:4]}...")
            else:
                logger.bind(tag=TAG).error("API 密钥为空")
        elif "404" in error_message:
            logger.bind(tag=TAG).error("未找到错误 (404)：API 端点可能不正确")
            if base_url:
                logger.bind(tag=TAG).debug(f"基础 URL 前缀: {base_url[:10]}...")
        elif "429" in error_message:
            logger.bind(tag=TAG).error("请求过多错误 (429)：已超过 API 速率限制")

        return ActionResponse(
            action=Action.RESPONSE,
            result=None,
            response=f"抱歉，我的视觉功能暂时出现问题: {error_type}"
        )

@register_function('eyes_iot', eyes_iot_function_desc, ToolType.SYSTEM_CTL)
def eyes_iot(conn=None):
    """小智的眼睛功能（IoT版本）

    通过IoT功能调用客户端的TakePhoto方法，然后使用AI视觉模型分析图像内容

    Args:
        conn: 连接处理器实例

    Returns:
        ActionResponse: 包含分析结果的响应对象
    """
    logger.bind(tag=TAG).info("启动IoT视觉功能")

    # 确保tmp目录存在
    os.makedirs("tmp", exist_ok=True)

    try:
        # 检查连接和设备管理器
        if not conn:
            logger.bind(tag=TAG).error("连接处理器为空")
            return ActionResponse(
                action=Action.RESPONSE,
                result=None,
                response="无法连接到设备，请确保设备在线。"
            )

        # 记录连接处理器的详细信息
        logger.bind(tag=TAG).info(f"连接处理器类型: {type(conn).__name__}")
        logger.bind(tag=TAG).info(f"连接处理器属性: {dir(conn)}")

        # 获取IoT设备管理器
        device_manager = getattr(conn, 'device_manager', None)
        if not device_manager:
            logger.bind(tag=TAG).error("设备管理器不可用")
            logger.bind(tag=TAG).error(f"连接处理器缺少 'device_manager' 属性")

            # 检查是否有类似的属性
            similar_attrs = [attr for attr in dir(conn) if 'device' in attr.lower() or 'manager' in attr.lower()]
            if similar_attrs:
                logger.bind(tag=TAG).error(f"找到类似的属性: {similar_attrs}")

            # 检查是否有 mcp_manager 属性
            if hasattr(conn, 'mcp_manager'):
                logger.bind(tag=TAG).error("找到 'mcp_manager' 属性，可能需要使用它来管理设备")
                # 尝试使用 mcp_manager
                try:
                    mcp_manager = conn.mcp_manager
                    logger.bind(tag=TAG).info(f"mcp_manager 类型: {type(mcp_manager).__name__}")
                    logger.bind(tag=TAG).info(f"mcp_manager 属性: {dir(mcp_manager)}")

                    # 检查是否有 get_devices 方法
                    if hasattr(mcp_manager, 'get_devices'):
                        logger.bind(tag=TAG).info("mcp_manager 有 get_devices 方法，尝试使用它")
                        device_manager = mcp_manager
                    else:
                        logger.bind(tag=TAG).error("mcp_manager 没有 get_devices 方法")
                except Exception as e:
                    logger.bind(tag=TAG).error(f"访问 mcp_manager 时出错: {str(e)}")

            # 检查是否有 func_handler 属性
            if hasattr(conn, 'func_handler'):
                logger.bind(tag=TAG).error("找到 'func_handler' 属性，可能需要使用它来调用函数")
                # 尝试使用 func_handler
                try:
                    func_handler = conn.func_handler
                    logger.bind(tag=TAG).info(f"func_handler 类型: {type(func_handler).__name__}")
                    logger.bind(tag=TAG).info(f"func_handler 属性: {dir(func_handler)}")
                except Exception as e:
                    logger.bind(tag=TAG).error(f"访问 func_handler 时出错: {str(e)}")

            # 如果仍然没有找到设备管理器，尝试使用 eyes_doubao 的方式
            logger.bind(tag=TAG).info("尝试使用 eyes_doubao 的方式获取图像")
            try:
                # 导入 eyes_doubao 中的函数
                from plugins_func.functions.eyes_doubao import get_client_ip, encode_image, process_image

                # 获取客户端 IP
                client_ip = get_client_ip(conn)
                logger.bind(tag=TAG).info(f"客户端 IP: {client_ip}")

                # 尝试通过 HTTP 获取图像
                import requests

                # 确保 tmp 目录存在
                os.makedirs("tmp", exist_ok=True)

                # 构建 URL
                url = f"http://{client_ip}/jpg"
                logger.bind(tag=TAG).info(f"尝试从 URL 获取图像: {url}")

                # 发送请求
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    # 获取用户ID（设备ID）
                    user_id = getattr(conn, 'device_id', None)
                    if not user_id:
                        # 如果没有设备ID，尝试从headers中获取
                        user_id = getattr(conn, 'headers', {}).get('device-id', 'unknown')

                    # 替换无效字符（Windows文件名不能包含: / \ * ? " < > |）
                    safe_user_id = user_id.replace(':', '_').replace('/', '_').replace('\\', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
                    logger.bind(tag=TAG).info(f"原始用户ID: {user_id}, 安全用户ID: {safe_user_id}")

                    # 使用固定的图片名称格式
                    image_path = f"tmp/iot_img_{safe_user_id}.jpeg"
                    with open(image_path, "wb") as f:
                        f.write(response.content)
                    logger.bind(tag=TAG).info(f"成功获取图像，保存到: {image_path}")

                    # 处理图像
                    processed_image_path = process_image(image_path)

                    # 图片转 base64
                    base64_image = encode_image(processed_image_path)

                    # 使用视觉模型分析图像
                    if VOLC_SDK_AVAILABLE:
                        return use_volcano_engine(base64_image)
                    elif OPENAI_AVAILABLE:
                        return use_openai(base64_image)
                else:
                    logger.bind(tag=TAG).error(f"HTTP 请求失败，状态码: {response.status_code}")
            except Exception as e:
                logger.bind(tag=TAG).error(f"尝试使用 eyes_doubao 的方式获取图像时出错: {str(e)}")

            # 如果所有尝试都失败，返回错误消息
            return ActionResponse(
                action=Action.RESPONSE,
                result=None,
                response="设备管理功能不可用，无法获取图像。请联系管理员检查系统配置。"
            )

        # 查找相机设备
        camera_device = None

        # 记录设备管理器的详细信息
        logger.bind(tag=TAG).info(f"设备管理器类型: {type(device_manager).__name__}")
        logger.bind(tag=TAG).info(f"设备管理器属性: {dir(device_manager)}")

        # 检查 get_devices 方法是否存在
        if not hasattr(device_manager, 'get_devices'):
            logger.bind(tag=TAG).error("设备管理器缺少 'get_devices' 方法")

            # 检查是否有类似的方法
            similar_methods = [method for method in dir(device_manager) if 'device' in method.lower() or 'get' in method.lower()]
            if similar_methods:
                logger.bind(tag=TAG).error(f"找到类似的方法: {similar_methods}")

            return ActionResponse(
                action=Action.RESPONSE,
                result=None,
                response="设备管理功能不完整，请检查系统配置。"
            )

        # 获取设备列表
        try:
            devices = device_manager.get_devices()
            logger.bind(tag=TAG).info(f"找到 {len(devices)} 个设备")

            # 记录每个设备的信息
            for i, device in enumerate(devices):
                logger.bind(tag=TAG).info(f"设备 {i+1} 类型: {type(device).__name__}")
                logger.bind(tag=TAG).info(f"设备 {i+1} 属性: {dir(device)}")

                # 检查设备是否有 id 属性
                if hasattr(device, 'id'):
                    logger.bind(tag=TAG).info(f"设备 {i+1} ID: {device.id}")

                # 检查设备是否有 type 属性
                if hasattr(device, 'type'):
                    logger.bind(tag=TAG).info(f"设备 {i+1} 类型: {device.type}")

                # 检查设备是否有 has_method 方法
                if hasattr(device, 'has_method'):
                    logger.bind(tag=TAG).info(f"设备 {i+1} 是否有 TakePhoto 方法: {device.has_method('TakePhoto')}")
                else:
                    logger.bind(tag=TAG).error(f"设备 {i+1} 缺少 'has_method' 方法")

                # 检查设备是否有 TakePhoto 方法
                if hasattr(device, 'has_method') and device.has_method("TakePhoto"):
                    camera_device = device
                    logger.bind(tag=TAG).info(f"找到相机设备: {device.id if hasattr(device, 'id') else '未知ID'}")
                    break
        except Exception as e:
            logger.bind(tag=TAG).error(f"获取设备列表失败: {str(e)}")
            return ActionResponse(
                action=Action.RESPONSE,
                result=None,
                response=f"获取设备列表失败，请检查系统配置。错误: {str(e)}"
            )

        if not camera_device:
            logger.bind(tag=TAG).error("未找到相机设备")
            return ActionResponse(
                action=Action.RESPONSE,
                result=None,
                response="未找到相机设备，请确保设备已连接并正确配置。"
            )

        # 调用TakePhoto方法
        logger.bind(tag=TAG).info(f"调用设备 {camera_device.id} 的TakePhoto方法")
        try:
            result = camera_device.call_method("TakePhoto", {})
            logger.bind(tag=TAG).info(f"TakePhoto方法调用结果: {result}")

            # 检查返回值是否表示成功
            if result is not None:
                # 如果返回值是字典，检查是否包含错误信息
                if isinstance(result, dict):
                    if 'error' in result or 'status' in result and result.get('status') != 'success':
                        error_msg = result.get('error', result.get('message', '未知错误'))
                        logger.bind(tag=TAG).error(f"TakePhoto方法返回错误: {error_msg}")
                        return ActionResponse(
                            action=Action.RESPONSE,
                            result=None,
                            response=f"拍照失败，摄像头可能出现问题。错误: {error_msg}"
                        )
                # 如果返回值是字符串，检查是否包含错误关键词
                elif isinstance(result, str):
                    error_keywords = ['error', 'fail', 'exception', '错误', '失败', '异常']
                    if any(keyword in result.lower() for keyword in error_keywords):
                        logger.bind(tag=TAG).error(f"TakePhoto方法返回错误信息: {result}")
                        return ActionResponse(
                            action=Action.RESPONSE,
                            result=None,
                            response=f"拍照失败，摄像头可能出现问题。错误: {result}"
                        )
                # 如果返回值是布尔值，检查是否为False
                elif isinstance(result, bool) and not result:
                    logger.bind(tag=TAG).error("TakePhoto方法返回False")
                    return ActionResponse(
                        action=Action.RESPONSE,
                        result=None,
                        response="拍照失败，摄像头可能出现问题。"
                    )
        except Exception as e:
            logger.bind(tag=TAG).error(f"调用TakePhoto方法失败: {str(e)}")
            return ActionResponse(
                action=Action.RESPONSE,
                result=None,
                response=f"拍照失败，请确保设备在线并且摄像头正常工作。错误: {str(e)}"
            )

        # 等待图片数据
        logger.bind(tag=TAG).info("等待图片数据...")
        image_path = wait_for_image(conn, timeout=10)

        if not image_path or not os.path.exists(image_path):
            logger.bind(tag=TAG).error("未收到图像数据")
            return ActionResponse(
                action=Action.RESPONSE,
                result=None,
                response="无法获取图像，请确保设备在线并且连接稳定。"
            )

        logger.bind(tag=TAG).info(f"收到图像数据: {image_path}")

        # 处理图像（如果OpenCV可用）
        processed_image_path = process_image(image_path)

        # 图片转base64
        base64_image = encode_image(processed_image_path)

        # 尝试使用火山引擎进行图像分析
        if VOLC_SDK_AVAILABLE:
            return use_volcano_engine(base64_image)
        # 如果火山引擎不可用，尝试使用OpenAI
        elif OPENAI_AVAILABLE:
            return use_openai(base64_image)
        # 如果两者都不可用，返回错误消息
        else:
            logger.bind(tag=TAG).error("没有可用的视觉AI服务")
            return ActionResponse(
                action=Action.RESPONSE,
                result=None,
                response="抱歉，我的视觉功能暂时不可用，因为系统缺少必要的AI服务。"
            )

    except Exception as e:
        logger.bind(tag=TAG).error(f"IoT视觉功能出错: {str(e)}")
        return ActionResponse(
            action=Action.RESPONSE,
            result=None,
            response=f"抱歉，我的视觉功能暂时出现问题: {str(e)}"
        )
