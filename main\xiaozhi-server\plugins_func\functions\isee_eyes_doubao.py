"""
豆包视觉识别插件
参考aiglass项目的实现，从API端动态获取豆包视觉大模型配置
"""

import asyncio
import base64
import json
import aiohttp
import os
from plugins_func.register import register_function, Action, ActionResponse, ToolType
from core.handle.sendAudioHandle import send_tts_message
from config.manage_api_client import get_agent_models
from loguru import logger

TAG = __name__

class DoubaoVisionProvider:
    """豆包视觉大模型提供者"""

    def __init__(self, conn):
        """从API端动态获取豆包视觉配置"""
        self.conn = conn
        self.api_key = ""
        self.base_url = ""
        self.model = ""
        self.max_tokens = 1000
        self.temperature = 0.7
        self.initialized = False

        # 尝试从API获取配置
        self._load_config_from_api()

    def _load_config_from_api(self):
        """从API端获取豆包视觉配置"""
        try:
            # 检查是否启用了API配置获取
            if not self.conn.config.get("read_config_from_api", False):
                logger.bind(tag=TAG).warning("未启用API配置获取，使用本地配置")
                self._load_local_config()
                return

            # 从API获取代理模型配置
            device_id = getattr(self.conn, 'device_id', 'default')
            client_id = getattr(self.conn, 'client_id', 'default')
            selected_module = self.conn.config.get("selected_module", {})

            # 添加视觉模块到选择的模块中
            if "Vision" not in selected_module:
                selected_module["Vision"] = "DoubaoVision"

            api_config = get_agent_models(device_id, client_id, selected_module)

            if api_config and "Vision" in api_config:
                vision_config = api_config["Vision"].get("DoubaoVision", {})

                self.api_key = vision_config.get("api_key", "")
                self.base_url = vision_config.get("base_url", "https://ark.cn-beijing.volces.com/api/v3")
                self.model = vision_config.get("model", "ep-20241230140547-8xqzr")
                self.max_tokens = vision_config.get("max_tokens", 1000)
                self.temperature = vision_config.get("temperature", 0.7)

                if self.api_key:
                    self.initialized = True
                    logger.bind(tag=TAG).info(f"从API获取豆包视觉配置成功，模型: {self.model}")
                else:
                    logger.bind(tag=TAG).warning("API返回的豆包视觉配置中缺少API密钥")
            else:
                logger.bind(tag=TAG).warning("API未返回豆包视觉配置，使用本地配置")
                self._load_local_config()

        except Exception as e:
            logger.bind(tag=TAG).error(f"从API获取豆包视觉配置失败: {e}")
            self._load_local_config()

    def _load_local_config(self):
        """加载本地配置作为备用"""
        try:
            vision_config = self.conn.config.get("Vision", {}).get("DoubaoVision", {})

            self.api_key = vision_config.get("api_key", "")
            self.base_url = vision_config.get("base_url", "https://ark.cn-beijing.volces.com/api/v3")
            self.model = vision_config.get("model", "ep-20241230140547-8xqzr")
            self.max_tokens = vision_config.get("max_tokens", 1000)
            self.temperature = vision_config.get("temperature", 0.7)

            if self.api_key:
                self.initialized = True
                logger.bind(tag=TAG).info(f"使用本地豆包视觉配置，模型: {self.model}")
            else:
                logger.bind(tag=TAG).warning("本地豆包视觉配置中缺少API密钥")

        except Exception as e:
            logger.bind(tag=TAG).error(f"加载本地豆包视觉配置失败: {e}")

    async def test_connection(self):
        """测试豆包视觉API连通性"""
        try:
            if not self.initialized:
                logger.bind(tag=TAG).warning("豆包视觉提供者未初始化，跳过连通性测试")
                return False

            # 创建一个简单的测试图片（1x1像素的白色图片）
            test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "这是一个连通性测试，请简单回复'连接正常'"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{test_image_base64}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 50,
                "temperature": 0.1
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=10)  # 连通性测试使用较短超时
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                        logger.bind(tag=TAG).info(f"豆包视觉API连通性测试成功: {content}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.bind(tag=TAG).error(f"豆包视觉API连通性测试失败: {response.status}, {error_text}")
                        return False

        except asyncio.TimeoutError:
            logger.bind(tag=TAG).error("豆包视觉API连通性测试超时")
            return False
        except Exception as e:
            logger.bind(tag=TAG).error(f"豆包视觉API连通性测试异常: {e}")
            return False

    async def analyze_image(self, image_path, question="请描述这张图片"):
        """分析图片并返回结果"""
        try:
            # 检查是否已初始化
            if not self.initialized:
                logger.bind(tag=TAG).error("豆包视觉提供者未正确初始化")
                return None
            # 读取图片并转换为base64
            if not os.path.exists(image_path):
                logger.bind(tag=TAG).error(f"图片文件不存在: {image_path}")
                return None

            with open(image_path, 'rb') as f:
                image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')

            # 调用豆包视觉API
            result = await self._call_doubao_vision_api(image_base64, question)

            if result:
                logger.bind(tag=TAG).info(f"豆包视觉分析成功: {result[:100]}...")
                return result
            else:
                logger.bind(tag=TAG).error("豆包视觉分析失败")
                return None

        except Exception as e:
            logger.bind(tag=TAG).error(f"分析图片时出错: {e}")
            return None

    async def _call_doubao_vision_api(self, image_base64, question):
        """调用豆包视觉API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": question
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": self.max_tokens,
                "temperature": self.temperature
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                        return content
                    else:
                        error_text = await response.text()
                        logger.bind(tag=TAG).error(f"豆包视觉API调用失败: {response.status}, {error_text}")
                        return None

        except Exception as e:
            logger.bind(tag=TAG).error(f"调用豆包视觉API异常: {e}")
            return None


# 全局豆包视觉提供者实例缓存
_doubao_vision_providers = {}

def get_doubao_vision_provider(conn):
    """获取豆包视觉提供者实例"""
    global _doubao_vision_providers

    # 使用连接ID作为缓存键
    conn_id = getattr(conn, 'session_id', 'default')

    if conn_id not in _doubao_vision_providers:
        _doubao_vision_providers[conn_id] = DoubaoVisionProvider(conn)

    return _doubao_vision_providers[conn_id]


# 拍照识图函数描述
capture_and_analyze_image_desc = {
    "type": "function",
    "function": {
        "name": "capture_and_analyze_image",
        "description": "拍照并使用豆包视觉大模型进行图片识别",
        "parameters": {
            "type": "object",
            "properties": {
                "question": {
                    "type": "string",
                    "description": "对图片询问的问题，默认为'请描述这张图片'"
                }
            },
            "required": []
        }
    }
}

@register_function("capture_and_analyze_image", capture_and_analyze_image_desc, ToolType.IOT_CTL)
def capture_and_analyze_image(conn, question="请描述这张图片"):
    """拍照识图功能"""
    try:
        # 获取豆包视觉提供者
        vision_provider = get_doubao_vision_provider(conn)

        # 模拟图片路径（实际应该从摄像头获取）
        # 这里需要集成实际的图片获取逻辑
        image_path = "tmp/latest_camera_image.jpg"

        # 异步处理图片分析
        async def process_image():
            try:
                result = await vision_provider.analyze_image(image_path, question)
                if result:
                    # 通过TTS播报结果
                    await send_tts_message(conn, "start", result)
                    logger.bind(tag=TAG).info(f"图片识别结果已发送TTS: {result[:50]}...")
                else:
                    await send_tts_message(conn, "start", "抱歉，图片识别失败")
            except Exception as e:
                logger.bind(tag=TAG).error(f"处理图片识别时出错: {e}")
                await send_tts_message(conn, "start", "图片识别过程中发生错误")

        # 启动异步任务
        asyncio.create_task(process_image())

        return ActionResponse(
            action=Action.RESPONSE,
            result="success",
            response=f"正在拍照识图，问题：{question}"
        )

    except Exception as e:
        logger.bind(tag=TAG).error(f"拍照识图失败: {e}")
        return ActionResponse(
            action=Action.ERROR,
            result=str(e),
            response="拍照识图失败，请稍后重试"
        )


def eyes_doubaoreal(conn, mode, image_path):
    """
    豆包视觉识别主函数
    参考aiglass项目的实现

    Args:
        conn: 连接对象
        mode: 模式 (1=识图, 2=指路)
        image_path: 图片路径

    Returns:
        ActionResponse: 包含识别结果的响应对象
    """
    try:
        # 获取豆包视觉提供者
        vision_provider = get_doubao_vision_provider(conn)

        # 根据模式确定问题
        if mode == 1:
            question = getattr(conn, 'prompt', "请描述这张图片")
        elif mode == 2:
            question = "请描述前方的路况和导航信息"
        else:
            question = "请描述这张图片"

        # 异步处理图片分析
        async def process_vision():
            try:
                result = await vision_provider.analyze_image(image_path, question)
                if result:
                    # 通过TTS播报结果
                    await send_tts_message(conn, "start", result)
                    logger.bind(tag=TAG).info(f"豆包视觉识别完成: {result[:50]}...")
                    return ActionResponse(
                        action=Action.RESPONSE,
                        result=result,
                        response=result
                    )
                else:
                    error_msg = "豆包视觉识别失败"
                    await send_tts_message(conn, "start", error_msg)
                    return ActionResponse(
                        action=Action.ERROR,
                        result="vision_failed",
                        response=error_msg
                    )
            except Exception as e:
                error_msg = f"豆包视觉识别过程中发生错误: {e}"
                logger.bind(tag=TAG).error(error_msg)
                await send_tts_message(conn, "start", "视觉识别过程中发生错误")
                return ActionResponse(
                    action=Action.ERROR,
                    result=str(e),
                    response=error_msg
                )

        # 启动异步任务并返回初始响应
        asyncio.create_task(process_vision())

        return ActionResponse(
            action=Action.RESPONSE,
            result="processing",
            response="正在进行豆包视觉识别..."
        )

    except Exception as e:
        logger.bind(tag=TAG).error(f"eyes_doubaoreal失败: {e}")
        return ActionResponse(
            action=Action.ERROR,
            result=str(e),
            response="豆包视觉识别初始化失败"
        )


# 初始化函数，在模块加载时调用
def initialize_doubao_vision(conn):
    """初始化豆包视觉服务"""
    try:
        vision_provider = get_doubao_vision_provider(conn)
        if vision_provider.initialized:
            logger.bind(tag=TAG).info("豆包视觉服务初始化完成")
            return vision_provider
        else:
            logger.bind(tag=TAG).warning("豆包视觉服务初始化失败：配置不完整")
            return None
    except Exception as e:
        logger.bind(tag=TAG).error(f"豆包视觉服务初始化失败: {e}")
        return None


async def test_doubao_vision_connectivity(conn):
    """测试豆包视觉API连通性"""
    try:
        logger.bind(tag=TAG).info("开始豆包视觉API连通性测试...")

        vision_provider = get_doubao_vision_provider(conn)
        if not vision_provider.initialized:
            logger.bind(tag=TAG).warning("豆包视觉提供者未初始化，跳过连通性测试")
            return False

        # 执行连通性测试
        is_connected = await vision_provider.test_connection()

        if is_connected:
            logger.bind(tag=TAG).info("✅ 豆包视觉API连通性测试通过")
        else:
            logger.bind(tag=TAG).error("❌ 豆包视觉API连通性测试失败")

        return is_connected

    except Exception as e:
        logger.bind(tag=TAG).error(f"豆包视觉API连通性测试异常: {e}")
        return False


# 服务器启动时的连通性测试
async def startup_vision_test(config):
    """服务器启动时的豆包视觉连通性测试"""
    try:
        logger.bind(tag=TAG).info("🚀 服务器启动 - 开始豆包视觉服务检测...")

        # 创建一个临时连接对象用于测试
        class TempConnection:
            def __init__(self, config):
                self.config = config
                self.device_id = "startup_test"
                self.client_id = "startup_test"
                self.session_id = "startup_test"

        temp_conn = TempConnection(config)

        # 执行连通性测试
        is_connected = await test_doubao_vision_connectivity(temp_conn)

        if is_connected:
            logger.bind(tag=TAG).info("🎉 豆包视觉服务启动检测完成 - 服务可用")
        else:
            logger.bind(tag=TAG).warning("⚠️  豆包视觉服务启动检测完成 - 服务不可用，请检查配置")

        return is_connected

    except Exception as e:
        logger.bind(tag=TAG).error(f"豆包视觉服务启动检测失败: {e}")
        return False


# 模块加载时自动执行启动测试
def _auto_startup_test():
    """模块加载时自动执行的启动测试"""
    try:
        import asyncio
        from config.config_loader import load_config

        logger.bind(tag=TAG).info("🚀 豆包视觉模块加载 - 开始连通性检测...")

        # 加载配置
        config = load_config()

        # 创建临时连接对象
        class TempConnection:
            def __init__(self, config):
                self.config = config
                self.device_id = "module_startup_test"
                self.client_id = "module_startup_test"
                self.session_id = "module_startup_test"

        temp_conn = TempConnection(config)

        # 获取豆包视觉提供者
        vision_provider = get_doubao_vision_provider(temp_conn)

        if vision_provider.initialized:
            # 执行连通性测试
            async def run_test():
                is_connected = await vision_provider.test_connection()
                if is_connected:
                    logger.bind(tag=TAG).info("✅ 豆包视觉模块连通性测试通过")
                else:
                    logger.bind(tag=TAG).warning("⚠️  豆包视觉模块连通性测试失败")
                return is_connected

            # 在新的事件循环中运行测试
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果已有事件循环在运行，创建任务
                    asyncio.create_task(run_test())
                else:
                    # 如果没有事件循环，直接运行
                    asyncio.run(run_test())
            except RuntimeError:
                # 如果无法获取事件循环，跳过测试
                logger.bind(tag=TAG).info("无法执行连通性测试，将在首次使用时检测")
        else:
            logger.bind(tag=TAG).warning("豆包视觉配置未完整，跳过连通性测试")

    except Exception as e:
        logger.bind(tag=TAG).info(f"豆包视觉模块启动检测跳过: {e}")


# 模块加载时自动执行测试
try:
    _auto_startup_test()
except Exception as e:
    # 静默处理，不影响模块加载
    pass
