"""
重构后的eyes_doubao插件，支持图像识别功能
使用缓存机制避免重复初始化LLM提供者
支持从配置文件动态配置URL模式和外网地址
"""

from plugins_func.register import register_function, ToolType, ActionResponse, Action
import requests
import base64
from config.logger import setup_logging
from datetime import datetime
from core.handle.iotHandle import send_iot_conn
import asyncio
import time
import os
import json
from core.utils import llm as llm_utils
from config.config_loader import load_config
from config.manage_api_client import ManageApiClient
from plugins_func.register import Action, ActionResponse
from core.utils.dialogue import Message, Dialogue

TAG = __name__
logger = setup_logging()

# 缓存LLM提供者实例，避免重复初始化
_llm_provider_cache = {}

# 自检状态，避免重复自检
_self_check_done = False

# 图像识别函数描述
eyes_function_desc = {
    "type": "function",
    "function": {
        "name": "isee_eyes_doubao",
        "description": "这个是摄像头工具,当问到前面是什么或者看到了什么之类的需要用眼镜看的场景的时候调用,会自动拍照上传并且识别分析",
        'parameters': {'type': 'object', 'properties': {}, 'required': []}
    }
}

# 默认API密钥和模型
DEFAULT_API_KEY = "0d9350ce7-d11f-4fe1-a2f0-708e496161b0"
DEFAULT_MODEL = "0doubao-1.5-vision-lite-250315"

# 默认提示词
DEFAULT_PROMPTS = {
    "0": "请描述一下这张图片",
    "1": "描绘下这个是什么",
    "2": "你现在是一个盲人ai助手通过摄像头传递过来的视频截图图像进行分析,帮忙给盲人解答看到的图片大致内容要简洁干练而且清晰重点凸出物品/方位/颜色/状态/距离摄像头的距离/前方是否有障碍物是否可以前行通行/尤其是需要注意提醒前方的遮挡物/下台阶/上台阶/坑/物体,需要以一个助理用人日常对话的方式介绍图片内容,介绍顺序由近到远,优先介绍主体内容,不要打招呼直接说画面中的内容,介绍完就不要说其他的了,除非有未识别清晰的文字询问用户是否靠近点看清楚",
    "3": "这是什么东西"
}

# 添加配置刷新相关变量
_config_last_refresh_time = 0
_config_refresh_interval = 60  # 配置刷新间隔，单位秒
_force_refresh_config = False  # 是否强制刷新配置

def fetch_llm_config_from_server(llm_code):
    """从服务器获取LLM配置，使用模型编码API

    Args:
        llm_code: LLM编码

    Returns:
        dict: LLM配置，如果获取失败则返回None
    """
    try:
        # 如果llm_code为None或空字符串，直接返回None
        if not llm_code:
            logger.bind(tag=TAG).warning("LLM编码为空，无法从服务器获取配置")
            return None

        logger.bind(tag=TAG).info(f"从服务器获取LLM配置: {llm_code}")

        # 检查ManageApiClient是否已初始化
        if not hasattr(ManageApiClient, '_instance') or ManageApiClient._instance is None:
            logger.bind(tag=TAG).warning("ManageApiClient未初始化，无法从服务器获取配置")
            return None

        # 使用ManageApiClient发送请求
        try:
            response = ManageApiClient._instance._execute_request(
                "GET",
                f"/config/model/{llm_code}"
            )
        except Exception as e:
            # 如果是资源不存在的错误，记录警告并返回None
            if "资源不存在" in str(e) or "未找到对应的模型数据" in str(e):
                logger.bind(tag=TAG).warning(f"服务器上未找到模型编码 {llm_code} 对应的配置，将使用默认配置")
                return None
            # 其他错误重新抛出
            raise

        # 如果响应为None（可能是因为服务器返回了错误）
        if not response:
            logger.bind(tag=TAG).warning(f"获取模型配置失败，服务器返回空响应")
            return None

        logger.bind(tag=TAG).info(f"response配置: {response}")
        
        # 检查configJson字段是否存在
        config_json = response.get("configJson", {})
        
        # 从configJson中提取必要的字段
        llm_config = {
            "type": config_json.get("type", "openai"),
            "model_name": config_json.get("model_name", response.get("modelName")),
            "api_key": config_json.get("api_key")
        }
        
        # 添加可选字段
        if "base_url" in config_json:
            llm_config["base_url"] = config_json["base_url"]
        
        # 检查API密钥是否为空
        if not llm_config["api_key"]:
            logger.bind(tag=TAG).warning(f"从服务器获取的API密钥为空，将使用默认API密钥")
            llm_config["api_key"] = DEFAULT_API_KEY
            
        # 检查模型名称是否为空
        if not llm_config["model_name"] or llm_config["model_name"] == "豆包识图模型":
            logger.bind(tag=TAG).warning(f"从服务器获取的模型名称为空或不是实际模型名称，将使用默认模型名称")
            llm_config["model_name"] = DEFAULT_MODEL

        logger.bind(tag=TAG).info(f"转换后的LLM配置: {llm_config}")
        return llm_config

    except Exception as e:
        logger.bind(tag=TAG).error(f"获取LLM配置时出错: {e}")
        import traceback
        logger.bind(tag=TAG).error(f"错误详情: {traceback.format_exc()}")
        return None
    



def get_llm_provider(llm_code):
    """获取LLM提供者实例
    
    Args:
        llm_code: LLM编码字符串
        
    Returns:
        LLMProviderBase: LLM提供者实例，如果获取失败则返回None
    """
    # 参数类型检查
    if not isinstance(llm_code, str):
        logger.bind(tag=TAG).error(f"llm_code必须是字符串，当前类型: {type(llm_code)}")
        return None
        
    logger.bind(tag=TAG).info(f"尝试获取LLM提供者，llm_code: {llm_code}")
    
    # 如果缓存中已有提供者实例，直接返回
    if llm_code in _llm_provider_cache:
        logger.bind(tag=TAG).info(f"使用缓存的LLM提供者: {llm_code}")
        return _llm_provider_cache[llm_code]
        
    # 尝试从服务器获取配置
    llm_config = fetch_llm_config_from_server(llm_code)
    
    # 如果没有找到配置，使用默认配置
    if not llm_config:
        logger.bind(tag=TAG).warning(f"未找到LLM配置，使用默认配置")
        llm_config = {
            "type": "openai",
            "api_key": DEFAULT_API_KEY,
            "model_name": DEFAULT_MODEL,
            "base_url": "https://ark.cn-beijing.volces.com/api/v3"
        }
        logger.bind(tag=TAG).info(f"默认配置: {llm_config}")
        
    try:
        # 获取LLM类型
        llm_type = llm_config.get("type", "openai")
        logger.bind(tag=TAG).info(f"创建LLM提供者，类型: {llm_type}")
        
        # 创建LLM提供者
        provider = llm_utils.create_instance(llm_type, llm_config)
        _llm_provider_cache[llm_code] = provider
        
        logger.bind(tag=TAG).info(f"成功创建LLM提供者: {type(provider)}")
        return provider
    except Exception as e:
        logger.bind(tag=TAG).error(f"创建LLM提供者失败: {e}")
        import traceback
        logger.bind(tag=TAG).error(f"错误详情: {traceback.format_exc()}")
        return None
    

    


# 添加一个清除缓存的函数
def clear_cache():
    """清除缓存，强制重新加载配置和LLM提供者实例"""
    global _llm_provider_cache, _force_refresh_config
    _llm_provider_cache = {}
    _force_refresh_config = True  # 标记需要强制刷新配置
    logger.bind(tag=TAG).info("已清除isee_eyes_doubao缓存，将在下次请求时重新加载配置")
    
def self_check_config():
    """自检配置，确保LLM配置正确"""
    global _self_check_done

    # 如果已经自检过，直接返回
    if _self_check_done:
        return

    logger.bind(tag=TAG).info("开始自检配置...")

    # 加载配置
    config = load_config()

    # 检查配置中是否有plugins.eye_doubao节点
    if "plugins" not in config or "eye_doubao" not in config["plugins"]:
        logger.bind(tag=TAG).warning(
            "配置文件中缺少plugins.eye_doubao节点，将使用默认配置")
    else:
        eye_doubao_config = config["plugins"]["eye_doubao"]

        # 检查llm_code配置
        # if "llm_code" not in eye_doubao_config:
        #     logger.bind(tag=TAG).warning(
        #         "配置文件中缺少plugins.eye_doubao.llm_code配置，将使用默认模型")
        # else:
        llm_code = eye_doubao_config["llm_code"]
        logger.bind(tag=TAG).info(f"配置的llm_code: {llm_code}")

        llm_provider = get_llm_provider(llm_code)

        if llm_provider:
            try:
                # 使用LLM提供者处理请求，使用正确格式的messages参数
                test_messages = [
                    {"role": "user", "content": "hello"}
                ]
                response_text = ""
                for chunk in llm_provider.response("", test_messages):
                    response_text += chunk

                logger.bind(tag=TAG).info(
                    f"LLM提供者处理请求成功，响应长度: {response_text}")
                return {"choices": [{"message": {"content": response_text}}]}
            except Exception as e:
                logger.bind(tag=TAG).error(f"使用LLM提供者处理请求失败: {str(e)}")

            # 检查LLM配置
            llm_config_found = False
            llm_codes = []

            for llm_id, llm_config in config.get("LLM", {}).items():
                code = llm_config.get("code")
                if code:
                    llm_codes.append(code)

                if code == llm_code:
                    llm_config_found = True
                    logger.bind(tag=TAG).info(
                        f"找到匹配的LLM配置: {llm_id}, 类型: {llm_config.get('type')}")

                    # 检查API密钥
                    if "api_key" not in llm_config or not llm_config["api_key"] or llm_config["api_key"] == "你的doubao web key":
                        logger.bind(tag=TAG).warning(
                            f"LLM配置 {llm_id} 中缺少有效的api_key，请检查配置")

                    # 检查模型名称
                    if "model_name" not in llm_config:
                        logger.bind(tag=TAG).warning(
                            f"LLM配置 {llm_id} 中缺少model_name，请检查配置")

                    break

    # 标记自检完成
    _self_check_done = True
    logger.bind(tag=TAG).info("自检配置完成")




# 执行自检
self_check_config()


def encode_image(image_path):
    """将图片转换为base64编码"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')



# 添加一个变量来存储上次获取的配置
_last_config = None

def get_eye_doubao_config(conn):
    """从连接对象中获取eye_doubao配置，支持定时刷新和强制刷新

    Args:
        conn: 连接对象，包含配置信息

    Returns:
        dict: eye_doubao配置字典
    """
    global _last_config, _config_last_refresh_time, _force_refresh_config
    
    # 默认配置
    default_config = {
        "llm_code": "0doubao-1.5-vision-lite-250315",
        "api_key": DEFAULT_API_KEY,
        "model": DEFAULT_MODEL,
        "prompts": DEFAULT_PROMPTS,
        "use_url_mode": False,
        "external_url": ""
    }

    # 检查连接对象中是否有LLM自定义属性中的提示词配置
    custom_prompts = None
    if conn and hasattr(conn, 'llm') and conn.llm:
        # 打印LLM对象的结构
        logger.bind(tag=TAG).info(f"LLM对象类型: {type(conn.llm)}")
        logger.bind(tag=TAG).info(f"LLM对象属性: {dir(conn.llm)}")
        
        # 检查LLM对象是否有自定义属性
        llm_custom_attrs = getattr(conn.llm, 'custom_attrs', None)
        logger.bind(tag=TAG).info(f"LLM自定义属性: {llm_custom_attrs}")
        
        if llm_custom_attrs:
            # 打印自定义属性的结构
            logger.bind(tag=TAG).info(f"LLM自定义属性类型: {type(llm_custom_attrs)}")
            logger.bind(tag=TAG).info(f"LLM自定义属性内容: {llm_custom_attrs}")
            
            # 尝试获取prompt1-5的配置
            prompt_attrs = {}
            for i in range(1, 6):
                prompt_key = f"prompt{i}"
                if prompt_key in llm_custom_attrs and llm_custom_attrs[prompt_key] and llm_custom_attrs[prompt_key].strip():
                    # 正确映射：prompt1对应索引"1"，prompt2对应索引"2"，以此类推
                    prompt_attrs[str(i)] = llm_custom_attrs[prompt_key]
                else:
                    # 如果提示词未配置或为空，使用默认提示词
                    default_prompt = DEFAULT_PROMPTS.get(str(i))
                    if default_prompt:
                        prompt_attrs[str(i)] = default_prompt
            
            if prompt_attrs:
                custom_prompts = prompt_attrs
                logger.bind(tag=TAG).info(f"从LLM自定义属性获取到提示词配置(包含默认值填充): {custom_prompts}")

    # 如果从自定义属性中没有获取到提示词，尝试从配置中获取
    if not custom_prompts and conn and hasattr(conn, 'config') and conn.config:
        # 检查配置中是否有LLM节点
        if 'LLM' in conn.config:
            # 获取当前选择的LLM模块名称
            selected_llm = conn.config.get('selected_module', {}).get('LLM')
            logger.bind(tag=TAG).info(f"当前选择的LLM模块: {selected_llm}")
            
            if selected_llm and selected_llm in conn.config['LLM']:
                # 获取LLM配置
                llm_config = conn.config['LLM'][selected_llm]
                logger.bind(tag=TAG).info(f"LLM配置: {llm_config}")
                
                # 尝试获取prompt1-5的配置
                prompt_attrs = {}
                for i in range(1, 6):
                    prompt_key = f"prompt{i}"
                    if prompt_key in llm_config and llm_config[prompt_key] and llm_config[prompt_key].strip():
                        # 正确映射：prompt1对应索引"1"，prompt2对应索引"2"，以此类推
                        prompt_attrs[str(i)] = llm_config[prompt_key]
                    else:
                        # 如果提示词未配置或为空，使用默认提示词
                        default_prompt = DEFAULT_PROMPTS.get(str(i))
                        if default_prompt:
                            prompt_attrs[str(i)] = default_prompt
                
                if prompt_attrs:
                    custom_prompts = prompt_attrs
                    logger.bind(tag=TAG).info(f"从配置中获取到提示词配置(包含默认值填充): {custom_prompts}")

    try:
        current_time = time.time()
        
        # 检查是否需要刷新配置
        need_refresh = (
            _force_refresh_config or  # 强制刷新
            _last_config is None or  # 首次加载
            (current_time - _config_last_refresh_time) > _config_refresh_interval  # 超过刷新间隔
        )
        
        if need_refresh:
            logger.bind(tag=TAG).info("刷新eye_doubao配置")
            # 重置强制刷新标志
            _force_refresh_config = False
            # 更新刷新时间
            _config_last_refresh_time = current_time
            
            # 重新加载配置 - 不修改load_config函数的方法
            from config.config_loader import load_config, _config_cache
            # 直接清除config_loader中的缓存
            import config.config_loader
            config.config_loader._config_cache = None
            # 然后正常加载配置
            config = load_config()
            
            # 如果配置中没有plugins节点，返回默认配置
            if "plugins" not in config:
                logger.bind(tag=TAG).warning("配置中没有plugins节点，使用默认配置")
                return default_config

            # 如果配置中没有eye_doubao节点，返回默认配置
            if "eye_doubao" not in config["plugins"]:
                logger.bind(tag=TAG).warning("配置中没有eye_doubao节点，使用默认配置")
                return default_config

            # 获取eye_doubao配置
            eye_doubao_config = config["plugins"]["eye_doubao"]
            logger.bind(tag=TAG).info(f"从配置文件获取到eye_doubao配置: {eye_doubao_config}")

            # 创建配置字典，使用默认值作为备选
            result_config = default_config.copy()

            # 更新配置
            if "llm_code" in eye_doubao_config:
                result_config["llm_code"] = eye_doubao_config["llm_code"]

            if "use_url_mode" in eye_doubao_config:
                result_config["use_url_mode"] = eye_doubao_config["use_url_mode"]

            if "external_url" in eye_doubao_config:
                result_config["external_url"] = eye_doubao_config["external_url"]

            # 处理prompts配置，确保它是一个字典
            if "prompts" in eye_doubao_config:
                try:
                    # 如果prompts是字符串，尝试解析为JSON
                    if isinstance(eye_doubao_config["prompts"], str):
                        result_config["prompts"] = json.loads(eye_doubao_config["prompts"])
                    else:
                        result_config["prompts"] = eye_doubao_config["prompts"]
                except json.JSONDecodeError as e:
                    logger.bind(tag=TAG).error(f"解析prompts配置失败: {str(e)}")
                    # 如果解析失败，使用默认提示词
                    result_config["prompts"] = DEFAULT_PROMPTS

            # 如果有自定义提示词，覆盖配置文件中的提示词
            if custom_prompts:
                # 记录原始提示词和自定义提示词
                logger.bind(tag=TAG).info(f"配置文件中的提示词: {result_config['prompts']}")
                logger.bind(tag=TAG).info(f"自定义提示词: {custom_prompts}")
                # 使用自定义提示词
                result_config["prompts"] = custom_prompts
                logger.bind(tag=TAG).info(f"最终使用的提示词配置: {result_config['prompts']}")

            # 更新缓存的配置
            _last_config = result_config
            return result_config
        else:
            # 使用缓存的配置
            return _last_config
    except Exception as e:
        logger.bind(tag=TAG).error(f"获取eye_doubao配置失败: {str(e)}")
        import traceback
        logger.bind(tag=TAG).error(f"错误详情: {traceback.format_exc()}")
        return default_config







def build_image_url(image_path, config):
    """构建图片URL

    如果配置了external_url，则使用外网地址，否则使用本地IP地址和端口。
    外网地址格式应为：http://example.com:8003/images

    Args:
        image_path: 图片路径
        config: 配置字典，包含external_url

    Returns:
        str: 图片URL
    """
    filename = os.path.basename(image_path)

    # 优先使用配置的外网地址
    if config.get("external_url"):
        # 直接使用配置的外网地址
        external_url = config['external_url'].rstrip('/')
        image_url = f"{external_url}/{filename}"
        logger.bind(tag=TAG).info(f"使用配置的外网地址: {image_url}")
    else:
        # 使用本地IP地址和端口
        from core.utils.util import get_local_ip
        server_ip = get_local_ip()
        server_port = 8003  # HTTP服务器端口
        image_url = f"http://{server_ip}:{server_port}/images/{filename}"
        logger.bind(tag=TAG).info(f"使用本地IP地址: {image_url}")

    return image_url


def build_content_with_image(text, image_path, config):
    """构建包含图像的内容"""
    if config["use_url_mode"]:
        # 构建图片URL
        image_url = build_image_url(image_path, config)

        # 使用HTTP URL
        content = [
            {"type": "text", "text": text},
            {
                "type": "image_url",
                "image_url": {
                    "url": image_url
                },
            },
        ]
        logger.bind(tag=TAG).info("使用HTTP URL方式传递图片")
    else:
        # 使用base64编码
        base64_image = encode_image(image_path)
        content = [
            {"type": "text", "text": text},
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{base64_image}"
                },
            },
        ]
        logger.bind(tag=TAG).info("使用base64编码方式传递图片")

    return content

# 添加MockResponse类
class MockResponse:
    """模拟OpenAI API响应的类"""
    def __init__(self, content):
        # 直接存储内容，不使用嵌套结构
        self.content = content
        # 兼容choices[0].message.content访问方式
        self.choices = [{"message": {"content": content}}]


def process_image_request(conn, messages, config):
    """处理图像请求，返回响应结果
    
    Args:
        conn: 连接对象，包含配置信息
        messages: 消息列表
        config: 配置字典
        
    Returns:
        MockResponse: 响应对象，包含choices[0].message.content
    """
    # 获取LLM编码
    llm_code = config.get("llm_code")
    if not llm_code:
        logger.bind(tag=TAG).error("配置中缺少llm_code")
        return MockResponse("图像识别失败: 配置错误")
        
    # 获取LLM提供者
    llm_provider = get_llm_provider(llm_code)
    
    if llm_provider:
        try:
            # 使用LLM提供者处理请求
            response_text = ""
            for chunk in llm_provider.response("", messages):
                response_text += chunk
                
            logger.bind(tag=TAG).info(f"LLM提供者处理请求成功，响应: {response_text[:100]}...")
            return MockResponse(response_text)
        except Exception as e:
            logger.bind(tag=TAG).error(f"使用LLM提供者处理请求失败: {str(e)}")
            # 如果处理失败，回退到使用火山引擎API
    
    # 回退到使用火山引擎API
    logger.bind(tag=TAG).warning("回退到使用火山引擎API")
    
    try:
        # 导入火山引擎SDK
        from volcenginesdkarkruntime import Ark
        
        # 初始化Ark客户端
        client = Ark(
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key=config["api_key"],
        )
        
        # 调用火山引擎API
        response = client.chat.completions.create(
            model=config["model"],
            messages=messages,
            max_tokens=500
        )
        
        logger.bind(tag=TAG).info(f"火山引擎API处理请求成功{response}")
        
        # 从火山引擎响应中提取文本内容
        if hasattr(response, 'choices') and len(response.choices) > 0:
            if hasattr(response.choices[0], 'message'):
                content = response.choices[0].message.content
            else:
                # 尝试作为字典访问
                content = response.choices[0].get('message', {}).get('content', '')
        else:
            # 如果没有choices字段，尝试其他方式获取内容
            content = getattr(response, 'content', str(response))
            
        # 返回标准化的响应对象
        return MockResponse(content)
    except Exception as e:
        logger.bind(tag=TAG).error(f"使用火山引擎API处理请求失败: {str(e)}")
        # 返回一个错误响应
        return MockResponse(f"图像处理失败: {str(e)}")
    


    


async def take_photo_async(conn, image_path):
    """异步拍照并等待图片上传

    Args:
        conn: 连接对象
        image_path: 图片保存路径

    Returns:
        bool: 拍照成功返回True

    Raises:
        TimeoutError: 等待图片上传超时
        ValueError: 图片文件大小为0
        Exception: 其他异常
    """
    logger.bind(tag=TAG).info("开始调用iot拍照并上传到服务器")

    # 删除可能存在的旧图片
    if os.path.exists(image_path):
        try:
            os.remove(image_path)
            logger.bind(tag=TAG).info(f"已删除旧图片: {image_path}")
        except Exception as e:
            logger.bind(tag=TAG).warning(f"删除旧图片失败: {str(e)}")

    # 发送拍照命令
    await send_iot_conn(conn, "Camera", "take_photo", {})
    logger.bind(tag=TAG).info("拍照命令已发送")

    # 等待图片上传
    timeout_seconds = 15
    start_time = time.time()

    # 等待图片文件出现
    while not os.path.exists(image_path):
        if time.time() - start_time > timeout_seconds:
            raise TimeoutError(f"等待图片超时（{timeout_seconds}秒）")
        await asyncio.sleep(0.2)

    # 等待图片写入完成
    file_size = 0
    for _ in range(5):  # 最多尝试5次
        file_size = os.path.getsize(image_path)
        if file_size > 0:
            break
        await asyncio.sleep(0.5)

    # 检查图片大小
    if file_size == 0:
        raise ValueError("图片文件大小为0")

    logger.bind(tag=TAG).info(f"图片上传完成，大小: {file_size} 字节")
    return True


@register_function('isee_eyes_doubao222', eyes_function_desc, ToolType.IOT_CTL)
def isee_eyes_doubao(conn):
    """处理图像识别请求

    拍照并识别图片内容，返回识别结果

    Args:
        conn: 连接对象

    Returns:
        ActionResponse: 处理结果
    """
    # 使用session_id作为图片文件名的一部分，避免冲突
    image_path = f"tmp/iot_img_{conn.session_id}.jpg" if conn and hasattr(
        conn, 'session_id') else "tmp/image.jpg"

    try:
        # 拍照并等待图片上传
        logger.bind(tag=TAG).info(f"开始调用拍照功能，图片将保存到: {image_path}")
        
        if hasattr(conn, 'loop') and conn.loop:
            # 在conn的事件循环中运行异步函数
            logger.bind(tag=TAG).info("使用现有事件循环发送拍照命令")
            future = asyncio.run_coroutine_threadsafe(
                take_photo_async(conn, image_path),
                conn.loop
            )
            # 等待结果返回
            future.result(timeout=20)  # 设置20秒超时
        else:
            # 如果没有conn.loop，创建一个新的事件循环
            logger.bind(tag=TAG).info("创建新事件循环发送拍照命令")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(take_photo_async(conn, image_path))
        
        logger.bind(tag=TAG).info(f"拍照完成，图片已保存到: {image_path}")
        
        # 调用本文件中的eyes_doubaoreal函数
        logger.bind(tag=TAG).info(f"开始调用eyes_doubaoreal处理图片")
        result = eyes_doubaoreal(conn, 0, image_path)
        logger.bind(tag=TAG).info(f"eyes_doubaoreal处理结果: {result}")
        
        # 处理返回结果
        if isinstance(result, ActionResponse) and result.action == Action.RESPONSE and result.response:
            # 获取原始响应文本
            response_text = result.response.strip()  # 去除首尾空白字符
            
            # 检查文本是否为空
            if not response_text:
                logger.bind(tag=TAG).warning("响应文本为空，不进行处理")
                return ActionResponse(Action.ERROR, "图像识别结果为空", "图像识别结果为空，请重试")
                
            logger.bind(tag=TAG).info(f"原始响应文本长度: {len(response_text)}")
            


        # 使用chat方法处理文本，设置tool_call=True表示这是工具调用的结果
            # conn.chat_isee(response_text, tool_call=False)
            conn.chat_isee(result.response, tool_call=True)
            # conn.speak_txt(conn, response_text)

            # 记录对话内容
            # if hasattr(conn, 'dialogue'):
            #     conn.dialogue.put(Message(role="assistant", content=response_text))
            
            # # 获取当前文本索引
            text_index = conn.tts_last_text_index + 1 if hasattr(conn, "tts_last_text_index") else 0
            # conn._handle_function_result(result, Action.RESPONSE)
            # # 使用process_text_segments处理文本分段
            logger.bind(tag=TAG).info(f"开始调用process_tool_result处理文本分段")
            # conn.process_tool_result(response_text)
            logger.bind(tag=TAG).info(f"process_tool_result处理完成")
            
            # 返回NONE类型的ActionResponse，表示已处理，不需要上层再处理
            return ActionResponse(Action.NONE, None, "图像识别已处理")
        
        # 如果不需要处理或处理失败，直接返回原始结果
        return result

    except TimeoutError as e:
        error_msg = f"图片上传超时，请稍后再试"
        logger.bind(tag=TAG).error(f"{error_msg}: {str(e)}")
        return ActionResponse(Action.ERROR, error_msg, error_msg)
    except ValueError as e:
        error_msg = f"图片文件无效，请稍后再试"
        logger.bind(tag=TAG).error(f"{error_msg}: {str(e)}")
        return ActionResponse(Action.ERROR, error_msg, error_msg)
    except Exception as e:
        error_msg = f"图像识别失败，请稍后再试"
        logger.bind(tag=TAG).error(f"{error_msg}: {str(e)}")
        import traceback
        logger.bind(tag=TAG).error(f"错误详情: {traceback.format_exc()}")
        return ActionResponse(Action.ERROR, error_msg, error_msg)




def build_message_with_image(prompt, image_path, config):
    """构建包含图像的消息"""
    if config["use_url_mode"]:
        # 构建图片URL
        image_url = build_image_url(image_path, config)

        # 构建消息
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url
                        }
                    },
                    {"type": "text", "text": prompt}
                ]
            }
        ]
    else:
        # 使用base64编码
        base64_image = encode_image(image_path)

        # 构建消息
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    },
                    {"type": "text", "text": prompt}
                ]
            }
        ]

    return messages

def eyes_doubaoreal(conn=None, mode=0, image_path=""):
    """处理不同模式的图像识别请求
    
    根据指定的模式识别图片内容，返回识别结果
    
    Args:
        conn: 连接对象
        mode: 识别模式，对应不同的提示词
        image_path: 图片路径
        
    Returns:
        ActionResponse: 处理结果
    """
    # 添加详细日志
    logger.bind(tag=TAG).info(f"开始处理图像识别请求: 模式={mode}, 图片路径={image_path}")
    
    # 检查图片是否存在
    if not os.path.exists(image_path) or os.path.getsize(image_path) == 0:
        error_msg = f"未检测到图片文件，请稍后再试"
        logger.bind(tag=TAG).error(f"{error_msg}: {image_path}")
        return ActionResponse(Action.ERROR, error_msg, error_msg)
        
    try:
        # 获取配置
        logger.bind(tag=TAG).debug(f"开始获取配置")
        config = get_eye_doubao_config(conn)
        logger.bind(tag=TAG).debug(f"配置获取成功: {config}")
        
        # 获取提示词
        prompt = None
        
        if mode == 0:
            # 当mode=0时，使用对话记录中的最后一条用户消息作为提示词
            if conn and hasattr(conn, 'dialogue') and conn.dialogue.dialogue:
                # 获取对话记录中的最后一条用户消息
                for msg in reversed(conn.dialogue.dialogue):
                    if msg.role == "user" and msg.content:
                        prompt = msg.content
                        logger.bind(tag=TAG).info(f"模式{mode}使用最后一条用户消息作为提示词: {prompt}")
                        break
                
                # 如果没有找到用户消息，使用默认提示词
                if not prompt:
                    prompt = config["prompts"].get("0", DEFAULT_PROMPTS.get("0"))
                    logger.bind(tag=TAG).warning(f"未找到用户消息，使用默认提示词: {prompt}")
            else:
                # 如果没有对话记录，使用默认提示词
                prompt = config["prompts"].get("0", DEFAULT_PROMPTS.get("0"))
                logger.bind(tag=TAG).warning(f"没有对话记录，使用默认提示词: {prompt}")
        else:
            # 对于其他模式，使用配置中的提示词
            mode_str = str(mode)
            prompt = config["prompts"].get(mode_str)
            
            # 如果提示词不存在，使用默认提示词
            if not prompt:
                prompt = config["prompts"].get("0", DEFAULT_PROMPTS.get("0"))
                logger.bind(tag=TAG).warning(f"模式{mode}的提示词不存在，使用默认提示词: {prompt}")
            else:
                logger.bind(tag=TAG).info(f"使用模式{mode}的提示词: {prompt}")

        # 记录使用的模式和提示词
        prompt_preview = prompt[:50] + "..." if prompt and len(prompt) > 50 else prompt
        logger.bind(tag=TAG).info(f"最终使用的提示词: {prompt_preview}")
        
        # 构建消息
        messages = build_message_with_image(prompt, image_path, config)
        
        # 处理图像请求
        response = process_image_request(conn, messages, config)
        # 打印响应对象的类型和内容，帮助调试
        logger.bind(tag=TAG).debug(f"响应对象类型: {type(response)}")
        logger.bind(tag=TAG).debug(f"响应对象内容: {response}")

        # 提取响应内容
        if isinstance(response, MockResponse):
            # 直接使用content属性
            result_text = response.content
        else:
            # 尝试从其他类型的响应中提取内容
            try:
                if hasattr(response, 'choices') and response.choices:
                    if hasattr(response.choices[0], 'message') and hasattr(response.choices[0].message, 'content'):
                        result_text = response.choices[0].message.content
                    elif isinstance(response.choices[0], dict):
                        message = response.choices[0].get('message', {})
                        if isinstance(message, dict):
                            result_text = message.get('content', '')
                        else:
                            result_text = str(message)
                    else:
                        result_text = str(response.choices[0])
                else:
                    result_text = str(response)
            except Exception as e:
                logger.bind(tag=TAG).error(f"提取响应内容时出错: {str(e)}")
                result_text = str(response)
        
        logger.bind(tag=TAG).info(f"图像识别成功，结果: {result_text[:100]}...")
        return ActionResponse(Action.RESPONSE, None, result_text)
        
    except Exception as e:
        error_msg = f"图像识别失败，请稍后再试"
        logger.bind(tag=TAG).error(f"{error_msg}: {str(e)}")
        import traceback
        logger.bind(tag=TAG).error(f"错误详情: {traceback.format_exc()}")
        return ActionResponse(Action.ERROR, error_msg, error_msg)

