"""
视觉分析插件 - 高级视觉分析功能
该插件允许用户指定分析提示，使用OpenAI的视觉模型分析图像内容。
"""

import os
import json
from plugins_func.register import register_function, ToolType, ActionResponse, Action
import requests
import base64
import cv2
from openai import OpenAI
from config.logger import setup_logging
from config.config_loader import load_config

# 设置日志
TAG = __name__
logger = setup_logging()

# 确保临时目录存在
os.makedirs("tmp", exist_ok=True)

# 定义函数描述，用于LLM函数调用
vision_analyze_desc = {
    "function": {
        "name": "vision_analyze",
        "description": "高级视觉分析功能，可以根据用户提示分析图像内容",
        'parameters': {
            'type': 'object',
            'properties': {
                'prompt': {
                    'type': 'string',
                    'description': '指导视觉模型如何分析图像的提示词，例如"描述这张图片"、"这张图片中有什么"、"识别图片中的文字"等'
                }
            },
            'required': ['prompt']
        }
    }
}

# 图片转base64函数
def encode_image(image_path):
    """将图片文件转换为base64编码

    Args:
        image_path: 图片文件路径

    Returns:
        str: base64编码的图片数据
    """
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def get_client_ip(conn=None):
    """获取客户端IP地址

    Args:
        conn: 连接处理器实例

    Returns:
        str: 客户端IP地址，如果无法获取则返回默认IP
    """
    try:
        if conn and hasattr(conn, 'client_ip'):
            return conn.client_ip

        # 如果无法从连接获取IP，尝试从配置获取
        config = load_config()
        device_ip = config.get("device", {}).get("ip")
        if device_ip:
            return device_ip
    except Exception as e:
        logger.bind(tag=TAG).error(f"获取客户端IP失败: {e}")

    # 如果都失败，返回默认IP
    return "***********"  # ESP32默认IP

@register_function('vision_analyze', vision_analyze_desc, ToolType.WAIT)
def vision_analyze(prompt, conn=None):
    """高级视觉分析功能

    从设备摄像头获取图像，并根据用户提示使用OpenAI的视觉模型分析图像内容

    Args:
        prompt: 指导视觉模型如何分析图像的提示词
        conn: 连接处理器实例

    Returns:
        ActionResponse: 包含分析结果的响应对象
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(conn)
        logger.bind(tag=TAG).info(f"尝试从设备 {client_ip} 获取图像")

        # 从设备获取图像
        url = f'http://{client_ip}/jpg'
        response = requests.get(url, timeout=5)

        if response.status_code != 200:
            logger.bind(tag=TAG).warning(f'图像下载失败，状态码: {response.status_code}')
            return ActionResponse(
                action=Action.REQLLM,
                result="我无法获取图像，请确保摄像头正常工作并且连接稳定。",
                response=""
            )

        # 保存原始图像
        with open('tmp/vision_image.jpg', 'wb') as file:
            file.write(response.content)
            logger.bind(tag=TAG).info('图像下载成功')

        # 读取图像并水平翻转（镜像处理）
        img = cv2.imread('tmp/vision_image.jpg')
        if img is None:
            logger.bind(tag=TAG).error('无法读取图像文件')
            return ActionResponse(
                action=Action.REQLLM,
                result="我无法处理获取到的图像，可能是图像格式不正确。",
                response=""
            )

        flipped_img = cv2.flip(img, 1)
        cv2.imwrite('tmp/vision_flipped.jpg', flipped_img)

        # 将图像转换为base64编码
        image_path = "tmp/vision_flipped.jpg"
        base64_image = encode_image(image_path)

        # 获取OpenAI配置
        config = load_config()
        llm_config = config.get("llm", {}).get("openai", {})
        api_key = llm_config.get("api_key")
        base_url = llm_config.get("base_url") or llm_config.get("url")
        model = "gpt-4-vision-preview"  # 使用支持视觉的模型

        if not api_key:
            logger.bind(tag=TAG).error("未找到OpenAI API密钥")
            return ActionResponse(
                action=Action.REQLLM,
                result="我无法使用视觉功能，因为未配置OpenAI API密钥。",
                response=""
            )

        # 创建OpenAI客户端
        client = OpenAI(api_key=api_key, base_url=base_url)

        # 准备消息
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ]

        # 调用OpenAI视觉模型
        logger.bind(tag=TAG).info(f"调用OpenAI视觉模型，提示词: {prompt}")
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=1000
        )

        # 获取分析结果
        analysis = response.choices[0].message.content
        logger.bind(tag=TAG).info(f"视觉分析完成，结果长度: {len(analysis)}")

        return ActionResponse(
            action=Action.REQLLM,
            result=analysis,
            response=""
        )

    except Exception as e:
        error_message = f"视觉功能出错: {str(e)}"
        logger.bind(tag=TAG).error(error_message)
        return ActionResponse(
            action=Action.REQLLM,
            result=f"抱歉，我的视觉功能暂时出现问题: {str(e)}",
            response=""
        )
