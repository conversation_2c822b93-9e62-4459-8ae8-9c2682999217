"""
视觉命令插件 - 提供直接的视觉命令，无需通过LLM函数调用
"""

import os
import json
import base64
import requests
import cv2
import openai
from openai import OpenAI
from config.logger import setup_logging
from plugins_func.register import register_function, ToolType, ActionResponse, Action
from config.config_loader import load_config

# 设置日志
TAG = __name__
logger = setup_logging()

# 确保临时目录存在
os.makedirs("tmp", exist_ok=True)

# 定义函数描述，用于LLM函数调用
look_function_desc = {
    "function": {
        "name": "look_at_me",
        "description": "让小智看着用户并描述所见",
        'parameters': {'type': 'object', 'properties': {}, 'required': []}
    }
}

def encode_image(image_path):
    """将图片文件转换为base64编码"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def get_client_ip(conn):
    """获取客户端IP地址"""
    try:
        return conn.client_ip
    except:
        # 如果无法从连接获取IP，返回默认IP
        return "***********"  # ESP32默认IP

@register_function('look_at_me', look_function_desc, ToolType.WAIT)
def look_at_me(conn=None):
    """让小智看着用户并描述所见

    这是一个简化的视觉命令，直接激活视觉功能并描述所见

    Args:
        conn: 连接处理器实例

    Returns:
        ActionResponse: 包含分析结果的响应对象
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(conn)
        logger.bind(tag=TAG).info(f"尝试从设备 {client_ip} 获取图像")

        # 从设备获取图像
        url = f'http://{client_ip}/jpg'
        response = requests.get(url, timeout=5)

        if response.status_code != 200:
            logger.bind(tag=TAG).warning(f'图像下载失败，状态码: {response.status_code}')
            return ActionResponse(
                action=Action.REQLLM,
                result="我无法看到你，请确保摄像头正常工作并且连接稳定。",
                response=""
            )

        # 保存原始图像
        with open('tmp/vision_image.jpg', 'wb') as file:
            file.write(response.content)
            logger.bind(tag=TAG).info('图像下载成功')

        # 读取图像并水平翻转（镜像处理）
        img = cv2.imread('tmp/vision_image.jpg')
        if img is None:
            logger.bind(tag=TAG).error('无法读取图像文件')
            return ActionResponse(
                action=Action.REQLLM,
                result="我无法处理获取到的图像，可能是图像格式不正确。",
                response=""
            )

        flipped_img = cv2.flip(img, 1)
        cv2.imwrite('tmp/vision_flipped.jpg', flipped_img)

        # 将图像转换为base64编码
        image_path = "tmp/vision_flipped.jpg"
        base64_image = encode_image(image_path)

        # 获取OpenAI配置
        config = load_config()
        llm_config = config.get("llm", {}).get("openai", {})
        api_key = llm_config.get("api_key")
        base_url = llm_config.get("base_url") or llm_config.get("url")
        model = llm_config.get("model_name") or "gpt-4-vision-preview"

        if not api_key:
            logger.bind(tag=TAG).error("未找到OpenAI API密钥")
            return ActionResponse(
                action=Action.REQLLM,
                result="我无法使用视觉功能，因为未配置OpenAI API密钥。",
                response=""
            )

        # 创建OpenAI客户端
        client = OpenAI(api_key=api_key, base_url=base_url)

        # 准备消息
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "请详细描述这张图片中的内容。如果有人，请描述他们的外貌、表情和动作。如果有物体，请描述它们的外观和位置。请用简洁自然的语言描述，就像你在和朋友聊天一样。"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ]

        # 调用OpenAI视觉模型
        logger.bind(tag=TAG).info("调用OpenAI视觉模型进行场景描述")
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=1000
        )

        # 获取分析结果
        analysis = response.choices[0].message.content
        logger.bind(tag=TAG).info(f"视觉分析完成，结果长度: {len(analysis)}")

        return ActionResponse(
            action=Action.REQLLM,
            result=analysis,
            response=""
        )

    except Exception as e:
        error_message = f"视觉功能出错: {str(e)}"
        logger.bind(tag=TAG).error(error_message)
        return ActionResponse(
            action=Action.REQLLM,
            result=f"抱歉，我的视觉功能暂时出现问题: {str(e)}",
            response=""
        )
