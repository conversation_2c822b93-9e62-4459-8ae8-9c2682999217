import os
import json
import asyncio
import threading
import time
import shutil
import uuid
import concurrent.futures
from aiohttp import web
from config.logger import setup_logging
from core.utils.util import get_local_ip

from plugins_func.register import Action, ActionResponse
from core.utils.dialogue import Message, Dialogue
from plugins_func.functions.isee_eyes_doubao import eyes_doubaoreal
# 不需要导入这些，因为我们直接使用conn对象的方法
# from plugins_func.register import Action, ActionResponse
# from core.dialogue import Message

TAG = __name__
logger = setup_logging()

# HTTP服务器实例
http_server = None
# HTTP服务器线程
http_thread = None
# 连接对象集合
active_connections = set()
# 处理图片的信号量，限制并发处理数量为1
image_processing_semaphore = asyncio.Semaphore(1)
# 用户处理状态字典，记录每个用户是否正在处理图片
user_processing_status = {}
# 创建线程池执行器
executor = concurrent.futures.ThreadPoolExecutor(max_workers=5)


class HttpServer:
    def __init__(self, port=8003):
        self.port = port
        self.app = web.Application()
        self.runner = None
        self.site = None
        self.logger = setup_logging()

        # 添加路由
        self.app.add_routes([
            web.post('/upload', self.handle_upload),
            web.get('/health', self.handle_health),
            web.get('/', self.handle_root),
            # 添加静态文件路由，用于访问图片
            web.get('/images/{filename}', self.handle_image)
        ])

        # 添加静态文件目录
        self.app.router.add_static('/static', 'tmp', show_index=False)

        # 创建临时目录
        os.makedirs("tmp", exist_ok=True)

    async def start(self):
        """启动HTTP服务器"""
        try:
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            self.site = web.TCPSite(self.runner, '0.0.0.0', self.port)
            await self.site.start()

            self.logger.bind(tag=TAG).info(
                f"HTTP服务器已启动，地址: http://{get_local_ip()}:{self.port}/"
            )
            self.logger.bind(tag=TAG).info(
                f"图片上传接口: http://{get_local_ip()}:{self.port}/upload"
            )

            # 保持服务器运行
            while True:
                await asyncio.sleep(3600)  # 每小时检查一次
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"启动HTTP服务器时出错: {str(e)}")
            raise

    async def stop(self):
        """停止HTTP服务器"""
        if self.site:
            await self.site.stop()
        if self.runner:
            await self.runner.cleanup()
        self.logger.bind(tag=TAG).info("HTTP服务器已停止")

    async def handle_upload(self, request):
        """处理图片上传请求"""
        try:
            # 检查请求类型
            content_type = request.headers.get('Content-Type', '')

            # 只从URL参数中获取会话ID、设备ID和模式信息
            session_id = request.query.get('session_id')
            device_id = request.query.get('device_id')  # 客户端使用device_id参数
            mode = request.query.get('mode')
            token = request.query.get('token')

            # 记录获取到的参数
            self.logger.bind(tag=TAG).info(
                f"从URL参数获取: token={token}, device_id={device_id}, mode={mode}")

            # 将模式转换为整数（如果存在）
            if mode and isinstance(mode, str) and mode.isdigit():
                mode = int(mode)

            # 记录请求信息
            self.logger.bind(tag=TAG).info(
                f"收到图片上传请求: session_id={session_id}, device_id={device_id}, mode={mode}")

            # 检查用户是否已经有正在处理的请求
            user_key = f"{session_id}_{device_id}" if session_id else device_id
            if user_key in user_processing_status and user_processing_status[user_key]:
                self.logger.bind(tag=TAG).info(
                    f"用户 {user_key} 已有正在处理的请求，拒绝新请求")
                return web.Response(
                    text="BUSY", 
                    status=429,
                    content_type="text/plain"
                )

            # 查找匹配的连接对象
            conn = None

            # 首先尝试使用session_id查找连接
            if session_id and active_connections:
                for connection in active_connections:
                    if session_id == connection.session_id:
                        conn = connection
                        self.logger.bind(tag=TAG).info(
                            f"找到匹配的连接对象，会话ID: {session_id}")
                        break

            # 如果仍然没有找到连接对象，则阻止上传图片和后续处理步骤
            if not conn:
                # 记录更详细的日志信息，帮助诊断问题
                self.logger.bind(tag=TAG).warning(
                    f"未找到匹配的连接对象，session_id: {session_id}, device_id: {device_id}")
                
                # 记录当前活跃连接的信息
                if active_connections:
                    self.logger.bind(tag=TAG).info(
                        f"当前活跃连接数: {len(active_connections)}")
                    for i, connection in enumerate(active_connections):
                        conn_session_id = getattr(
                            connection, 'session_id', 'None')
                        conn_device_id = getattr(
                            connection, 'device_id', 'None')
                        self.logger.bind(tag=TAG).info(
                            f"连接 {i+1}: session_id={conn_session_id}, device_id={conn_device_id}, tokens={connection.auth.tokens}")
                else:
                    self.logger.bind(tag=TAG).warning("当前没有活跃的连接")

                return web.Response(
                    text="DISCONNECTED", 
                    status=404,
                    content_type="text/plain"
                )
            # 重置超时计时器
            await conn.reset_timeout()

            if content_type.startswith('image/'):
                # 直接处理二进制图片上传
                image_data = await request.read()

                # 保存图片
                if session_id:
                    # 使用用户ID作为文件名
                    file_path = f"tmp/iot_img_{session_id}.jpg"
                else:
                    # 使用默认文件名
                    file_path = "tmp/image.jpg"

                # 确保目录存在
                os.makedirs(os.path.dirname(file_path), exist_ok=True)

                # 先写入临时文件，然后重命名，确保原子性操作
                temp_file_path = f"{file_path}.tmp"
                with open(temp_file_path, 'wb') as f:
                    f.write(image_data)
                    # 确保数据写入磁盘
                    f.flush()
                    os.fsync(f.fileno())

                # 重命名文件（原子操作）
                if os.path.exists(file_path):
                    os.remove(file_path)  # 如果目标文件已存在，先删除
                os.rename(temp_file_path, file_path)

                # 确保文件权限正确
                os.chmod(file_path, 0o644)

                self.logger.bind(tag=TAG).info(
                    f"图片已保存到: {file_path}, 大小: {len(image_data)} 字节, 模式: {mode}")

                # 构建图片URL
                filename = os.path.basename(file_path)
                image_url = f"http://{get_local_ip()}:{self.port}/images/{filename}"
                self.logger.bind(tag=TAG).info(f"图片URL: {image_url}")

                result = ""
                if mode != -1:
                  # 处理图片
                    result = await self.process_image(file_path, device_id, mode, session_id, conn)

                return web.Response(
                    text=result if result else "OK",
                    status=200,
                    content_type="text/plain"
                )

            else:
                return web.Response(
                    text="UNSUPPORTED_TYPE", 
                    status=415,
                    content_type="text/plain"
                )

        except Exception as e:
            # 记录详细错误日志
            self.logger.bind(tag=TAG).error(f"处理图片上传时出错: {str(e)}")
            # 简单文本响应
            return web.Response(
                text="ERROR", 
                status=500,
                content_type="text/plain"
            )

    async def process_image(self, image_path, user_id=None, mode=None, session_id=None, conn=None):
        """处理上传的图片

        Args:
            image_path: 图片路径
            user_id: 用户ID/设备ID
            mode: 处理模式（1=识图模式，2=指路模式）
            session_id: 会话ID

        Returns:
            str: 处理结果
        """
        # 生成用户唯一标识
        user_key = f"{session_id}_{user_id}" if session_id else user_id

        # 设置用户处理状态为正在处理
        user_processing_status[user_key] = True

        # 使用信号量限制并发处理
        async with image_processing_semaphore:
            try:
                # 记录处理信息
                self.logger.bind(tag=TAG).info(
                    f"开始处理图片: {image_path}, 设备ID: {user_id}, 会话ID: {session_id}, 模式: {mode}")

                # 定义处理图片的函数
                def process_image_task(conn, mode, image_path):
                    try:
                        # 检查连接是否已关闭
                        if not conn or (hasattr(conn, 'stop_event') and conn.stop_event.is_set()):
                            logger.bind(tag=TAG).warning(f"连接已关闭，取消图片处理")
                            return None
                        
                        # 调用eyes_doubao函数处理图片
                        logger.bind(tag=TAG).info(
                            f"在线程池中开始调用eyes_doubao函数处理图片")
                        start_time = time.time()
                        result = eyes_doubaoreal(conn, mode, image_path)
                        end_time = time.time()
                        logger.bind(tag=TAG).info(
                            f"eyes_doubao函数处理完成，耗时: {end_time - start_time:.2f}秒")
                        return result
                    except Exception as e:
                        logger.bind(tag=TAG).error(f"处理图片时出错: {str(e)}")
                        raise  # 重新抛出异常，让调用者处理

                # 使用线程池执行任务，并等待结果
                loop = asyncio.get_event_loop()
                try:
                    # 在处理前检查连接状态
                    if not conn or (hasattr(conn, 'stop_event') and conn.stop_event.is_set()):
                        self.logger.bind(tag=TAG).warning(f"连接已关闭，取消图片处理")
                        return "连接已关闭，无法处理图片"
                    
                    # 在线程池中执行耗时操作，但不阻塞事件循环
                    self.logger.bind(tag=TAG).info(f"提交图片处理任务到线程池")
                    result = await loop.run_in_executor(executor, process_image_task, conn, mode, image_path)

                    # 安全地检查连接状态
                    try:
                        # 首先检查连接是否仍然有效
                        if conn and hasattr(conn, 'executor') and conn.executor and not conn.stop_event.is_set():
                            # 处理返回的ActionResponse
                            if result and hasattr(result, 'action'):
                                # 使用连接对象的_handle_function_result方法处理结果
                                # 获取当前文本索引
                                text_index = conn.tts_last_text_index + 1 if hasattr(conn, "tts_last_text_index") else 0
                                
                                # 如果是RESPONSE类型的结果，使用process_text_segments处理长文本
                                if result.action == Action.RESPONSE and result.response:
                                    try:
                                        self.logger.bind(tag=TAG).info(f"准备处理RESPONSE类型结果，文本长度: {len(result.response)}")
                                        # 记录对话内容
                                        conn.dialogue.put(Message(role="assistant", content=result.response))
                                        # 再次检查连接状态
                                        if not conn.stop_event.is_set():
                                            self.logger.bind(tag=TAG).info(f"开始调用process_text_segments处理文本分段")
                                            # 处理文本分句
                                            # conn.process_text_segments(result.response, text_index)

                                            # conn.chat_isee(result.response, tool_call=True)
                                            conn._handle_function_result(result, Action.RESPONSE)
                                            self.logger.bind(tag=TAG).info(f"process_text_segments处理完成")
                                        else:
                                            self.logger.bind(tag=TAG).warning(f"连接已关闭，无法处理文本分段")
                                    except Exception as text_error:
                                        self.logger.bind(tag=TAG).error(f"处理文本分段时出错: {str(text_error)}")
                                else:
                                    self.logger.bind(tag=TAG).info(f"处理非RESPONSE类型结果: {result.action}")
                                    # 对于其他类型的结果，使用原有的处理方法
                                    conn._handle_function_result(result, None)
                                self.logger.bind(tag=TAG).info(f"图片处理完成并已处理结果")
                        else:
                            self.logger.bind(tag=TAG).warning(f"连接已关闭或无效，无法处理图片结果")
                    except Exception as conn_error:
                        self.logger.bind(tag=TAG).warning(f"处理图片结果时出错，可能是连接已关闭: {str(conn_error)}")

                    # 返回处理结果
                    if result and hasattr(result, 'response'):
                        return result.response
                    return "图片处理完成"
                except Exception as e:
                    self.logger.bind(tag=TAG).error(f"处理图片时出错: {str(e)}")
                    return f"处理图片时出错: {str(e)}"
                finally:
                    # 无论处理成功还是失败，都将用户处理状态设置为未处理
                    user_processing_status[user_key] = False
                    self.logger.bind(tag=TAG).info(f"用户 {user_key} 的图片处理已完成")
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"提交图片处理任务时出错: {str(e)}")
                user_processing_status[user_key] = False
                return f"处理图片时出错: {str(e)}"


    async def handle_health(self, _):
        """健康检查接口"""
        return web.json_response({
            'status': 'ok',
            'message': 'HTTP服务器运行正常'
        })

    async def handle_root(self, _):
        """根路径处理"""
        return web.Response(text="小智服务器HTTP接口正在运行", content_type="text/plain")

    async def handle_image(self, request):
        """处理图片请求

        Args:
            request: HTTP请求对象

        Returns:
            web.Response: 图片响应
        """
        try:
            # 获取文件名
            filename = request.match_info['filename']

            # 构建文件路径
            file_path = os.path.join('tmp', filename)

            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.logger.bind(tag=TAG).warning(f"请求的图片不存在: {file_path}")
                return web.Response(status=404, text="图片不存在")

            # 检查文件是否是图片
            if not filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
                self.logger.bind(tag=TAG).warning(f"请求的文件不是图片: {file_path}")
                return web.Response(status=403, text="只允许访问图片文件")

            # 确定内容类型
            content_type = 'image/jpeg'  # 默认为JPEG
            if filename.lower().endswith('.png'):
                content_type = 'image/png'
            elif filename.lower().endswith('.gif'):
                content_type = 'image/gif'

            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # 记录访问日志
            self.logger.bind(tag=TAG).info(f"提供图片访问: {file_path}, 大小: {len(file_data)} 字节")

            # 返回图片
            return web.Response(body=file_data, content_type=content_type)

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"处理图片请求时出错: {str(e)}")
            return web.Response(status=500, text=f"处理图片请求时出错: {str(e)}")


def start_http_server_thread(port=8003):
    """在新线程中启动HTTP服务器

    Args:
        port: HTTP服务器端口号
    """
    global http_server

    # 创建新的事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    # 创建HTTP服务器
    http_server = HttpServer(port)

    # 启动HTTP服务器
    try:
        loop.run_until_complete(http_server.start())
    except Exception as e:
        logger.bind(tag=TAG).error(f"HTTP服务器线程出错: {str(e)}")
    finally:
        loop.close()


def register_connections(connections):
    """注册活跃的WebSocket连接处理器集合

    Args:
        connections: WebSocketServer中的活跃连接集合
    """
    global active_connections
    active_connections = connections
    logger.bind(tag=TAG).info(f"已注册 {len(connections)} 个活跃连接")


# 定义一个函数来启动HTTP服务器
def auto_start_http_server():
    """自动启动HTTP服务器"""
    global http_thread

    # 检查HTTP服务器是否已经启动
    if http_thread and http_thread.is_alive():
        logger.bind(tag=TAG).info("HTTP服务器已经在运行中，不需要重新启动")
        return

    # 启动HTTP服务器
    logger.bind(tag=TAG).info("正在自动启动HTTP服务器...")
    http_thread = threading.Thread(
        target=start_http_server_thread,
        args=(8003,),
        daemon=True
    )
    http_thread.start()
    logger.bind(tag=TAG).info(f"HTTP服务器线程已启动，端口: 8003")

# 自动启动HTTP服务器
auto_start_http_server()
