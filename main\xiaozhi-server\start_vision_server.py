#!/usr/bin/env python3
"""
启动视觉HTTP服务器的测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.config_loader import load_config
from config.logger import setup_logging
from vision_http_server import start_vision_http_server

TAG = __name__
logger = setup_logging()

async def main():
    """启动视觉HTTP服务器"""
    try:
        logger.bind(tag=TAG).info("🚀 启动视觉HTTP服务器测试...")
        
        # 加载配置
        config = load_config()
        
        # 启动视觉HTTP服务器
        await start_vision_http_server(config)
        
        # 保持运行
        logger.bind(tag=TAG).info("✅ 视觉HTTP服务器已启动，按Ctrl+C退出")
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.bind(tag=TAG).info("收到退出信号，正在关闭...")
    except Exception as e:
        logger.bind(tag=TAG).error(f"启动失败: {e}")
    finally:
        logger.bind(tag=TAG).info("视觉HTTP服务器已停止")

if __name__ == "__main__":
    asyncio.run(main())
