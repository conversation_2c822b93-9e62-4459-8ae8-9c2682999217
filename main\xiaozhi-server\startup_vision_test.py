"""
服务器启动时的豆包视觉连通性测试示例
在主应用程序中调用此文件进行启动检测
"""

import asyncio
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

async def run_startup_vision_test(config):
    """运行豆包视觉启动检测"""
    try:
        logger.bind(tag=TAG).info("🚀 开始豆包视觉服务启动检测...")
        
        # 导入豆包视觉模块
        from plugins_func.functions.isee_eyes_doubao import startup_vision_test
        
        # 执行连通性测试
        is_connected = await startup_vision_test(config)
        
        if is_connected:
            logger.bind(tag=TAG).info("✅ 豆包视觉服务启动检测完成 - 服务可用")
        else:
            logger.bind(tag=TAG).warning("⚠️  豆包视觉服务启动检测完成 - 服务不可用")
            
        return is_connected
        
    except ImportError as e:
        logger.bind(tag=TAG).info("豆包视觉模块未安装，跳过连通性测试")
        return False
    except Exception as e:
        logger.bind(tag=TAG).error(f"豆包视觉启动检测失败: {e}")
        return False

if __name__ == "__main__":
    # 测试用例
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    from config.config_loader import load_config
    
    async def test():
        config = load_config()
        await run_startup_vision_test(config)
    
    asyncio.run(test())
