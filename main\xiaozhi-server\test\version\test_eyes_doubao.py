import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.abspath(os.path.join(current_dir, '..', '..'))
sys.path.insert(0, root_dir)

import requests
import json
import base64
from unittest.mock import patch, MagicMock
from config.logger import setup_logging
from config.settings import load_config
from plugins_func.functions.eyes_doubao import eyes_doubao, use_openai, use_volcano_engine
from plugins_func.register import ActionResponse, Action

# 设置日志
TAG = __name__
logger = setup_logging()

# 读取已有的图片
def read_image():
    """读取已有的图片

    Returns:
        tuple: (图片路径, 二进制数据, base64编码的字符串)
    """
    # 图片路径 - 尝试多个可能的位置
    possible_paths = [
        os.path.join(root_dir, "img.png"),  # 项目根目录
        os.path.join(os.path.dirname(root_dir), "img.png"),  # 上一级目录
        os.path.join(root_dir, "test", "img.png"),  # test 目录
        os.path.join(root_dir, "test", "version", "img.png"),  # test/version 目录
        "img.png"  # 当前工作目录
    ]

    # 查找第一个存在的图片路径
    image_path = None
    for path in possible_paths:
        if os.path.exists(path):
            image_path = path
            break

    # 如果没有找到图片，使用第一个路径
    if image_path is None:
        image_path = possible_paths[0]

    try:
        # 检查图片是否存在
        if not os.path.exists(image_path):
            logger.bind(tag=TAG).error(f"图片不存在: {image_path}")
            return None, None, None

        # 读取图片
        with open(image_path, "rb") as f:
            image_binary = f.read()

        # 转换为 base64
        image_base64 = base64.b64encode(image_binary).decode('utf-8')

        logger.bind(tag=TAG).info(f"成功读取图片: {image_path}")
        return image_path, image_binary, image_base64

    except Exception as e:
        logger.bind(tag=TAG).error(f"读取图片失败: {str(e)}")
        return None, None, None

# 读取图片
IMAGE_PATH, IMAGE_BINARY, IMAGE_BASE64 = read_image()

def mock_requests_get(*args, **kwargs):
    """模拟 requests.get 函数，返回模拟的图片数据"""
    mock_response = MagicMock()
    mock_response.status_code = 200
    # 使用实际读取的图片数据
    mock_response.content = IMAGE_BINARY
    return mock_response

def mock_openai_client(*args, **kwargs):
    """模拟 OpenAI 客户端"""
    from openai.types.chat import ChatCompletion, ChatCompletionMessage, ChatCompletionMessageToolCall
    from openai.types.chat.chat_completion import Choice
    from openai.types.chat.chat_completion_message_tool_call import Function

    # 创建一个真实的 OpenAI 响应对象，而不是 MagicMock
    function_call = ChatCompletionMessageToolCall(
        id="call_123",
        type="function",
        function=Function(
            name="eyes_doubao",
            arguments="{}"
        )
    )

    message = ChatCompletionMessage(
        content="我需要使用我的视觉功能来回答这个问题。",
        role="assistant",
        tool_calls=[function_call]
    )

    choice = Choice(
        finish_reason="tool_calls",
        index=0,
        message=message
    )

    completion = ChatCompletion(
        id="chatcmpl-123",
        choices=[choice],
        created=1677858242,
        model="gpt-3.5-turbo",
        object="chat.completion"
    )

    # 创建模拟客户端
    mock_client = MagicMock()
    mock_client.chat.completions.create.return_value = completion

    return mock_client

def test_direct():
    """直接测试 eyes_doubao 函数，不使用模拟"""
    logger.bind(tag=TAG).info("开始直接测试 eyes_doubao 函数（不使用模拟）")

    # 确保 tmp 目录存在
    os.makedirs("tmp", exist_ok=True)

    try:
        # 调用 eyes_doubao 函数
        result = eyes_doubao()

        # 打印详细结果
        logger.bind(tag=TAG).info(f"eyes_doubao 函数返回结果: {result}")
        print("\n=== eyes_doubao 函数返回结果 ===")
        print(f"结果类型: {type(result)}")
        print(f"Action: {result.action}")
        print(f"Result: {result.result}")
        print(f"Response: {result.response}")

        # 如果响应很长，打印前100个字符和后100个字符
        if result.response and len(result.response) > 200:
            print("\n响应内容太长，只显示部分内容:")
            print(f"前100个字符: {result.response[:100]}...")
            print(f"后100个字符: ...{result.response[-100:]}")
        elif result.response:
            print(f"\n完整响应内容:\n{result.response}")
        else:
            print("\n响应内容为空")

    except Exception as e:
        logger.bind(tag=TAG).error(f"测试 eyes_doubao 函数时出错: {str(e)}")
        print(f"测试 eyes_doubao 函数时出错: {str(e)}")
        # 打印详细的错误堆栈
        import traceback
        traceback.print_exc()

def test_with_mock():
    """使用模拟数据测试 eyes_doubao 函数"""
    logger.bind(tag=TAG).info("开始使用模拟数据测试 eyes_doubao 函数")

    # 确保 tmp 目录存在
    os.makedirs("tmp", exist_ok=True)

    # 使用 patch 模拟 requests.get 函数
    with patch('requests.get', side_effect=mock_requests_get):
        # 使用 patch 模拟 OpenAI 客户端
        with patch('openai.OpenAI', side_effect=mock_openai_client):
            try:
                # 调用 eyes_doubao 函数
                result = eyes_doubao()

                # 打印详细结果
                logger.bind(tag=TAG).info(f"eyes_doubao 函数返回结果: {result}")
                print("\n=== eyes_doubao 函数返回结果（使用模拟数据）===")
                print(f"结果类型: {type(result)}")
                print(f"Action: {result.action}")
                print(f"Result: {result.result}")
                print(f"Response: {result.response}")

                # 如果响应很长，打印前100个字符和后100个字符
                if result.response and len(result.response) > 200:
                    print("\n响应内容太长，只显示部分内容:")
                    print(f"前100个字符: {result.response[:100]}...")
                    print(f"后100个字符: ...{result.response[-100:]}")
                elif result.response:
                    print(f"\n完整响应内容:\n{result.response}")
                else:
                    print("\n响应内容为空")

            except Exception as e:
                logger.bind(tag=TAG).error(f"测试 eyes_doubao 函数时出错: {str(e)}")
                print(f"测试 eyes_doubao 函数时出错: {str(e)}")
                # 打印详细的错误堆栈
                import traceback
                traceback.print_exc()

def test_llm_function_call():
    """测试 LLM 是否能正确调用视觉意图"""
    logger.bind(tag=TAG).info("开始测试 LLM 调用视觉意图")

    try:
        print("\n=== 简化版测试：直接使用豆包视觉模型 ===")

        # 直接硬编码豆包视觉模型配置
        llm_config = {
            "api_key": "d9350ce7-d11f-4fe1-a2f0-708e496161b0",  # API 密钥
            "base_url": "https://ark.cn-beijing.volces.com/api/v3",  # 豆包 API 端点
            "model_name": "doubao-1-5-vision-pro-32k-250115",  # 豆包视觉模型
            "type": "openai"  # 使用 OpenAI 兼容接口
        }

        # 打印使用的配置
        print(f"使用豆包视觉模型配置: {llm_config}")

        # 测试提示
        prompt = "小智，请详细描述一下你在这张图片中看到了什么？"

        # 读取图片
        image_path, image_binary, image_base64 = read_image()

        if not image_path or not image_base64:
            print("警告: 没有找到图片，创建一个简单的测试图片")
            # 创建一个简单的测试图片
            import numpy as np
            from PIL import Image
            import io

            # 创建一个简单的彩色图片
            img = np.zeros((100, 100, 3), dtype=np.uint8)
            img[30:70, 30:70] = [255, 0, 0]  # 红色方块

            # 转换为 PIL 图片
            pil_img = Image.fromarray(img)

            # 保存图片
            image_path = os.path.join(root_dir, "test_image.png")
            pil_img.save(image_path)

            # 转换为 base64
            buffer = io.BytesIO()
            pil_img.save(buffer, format="PNG")
            image_binary = buffer.getvalue()
            image_base64 = base64.b64encode(image_binary).decode('utf-8')

            print(f"已创建测试图片: {image_path}")

        # 创建对话历史，包含图片
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}}
                ]
            }
        ]
        print("已添加图片到消息中")

        # 导入 OpenAI 模块
        import openai

        # 使用 OpenAI 客户端直接调用
        client = openai.OpenAI(api_key=llm_config["api_key"], base_url=llm_config["base_url"])

        print("\n=== 发送请求到豆包视觉模型 ===")

        # 直接调用模型，不使用函数调用
        response = client.chat.completions.create(
            model=llm_config["model_name"],
            messages=messages
        )

        # 打印模型回复
        print("\n=== 豆包视觉模型回复 ===")
        if hasattr(response.choices[0].message, 'content'):
            content = response.choices[0].message.content
            print(f"模型回复: {content}")
        else:
            print("模型没有回复内容")
            print(f"完整响应: {response}")

            # 打印完整的响应对象，方便调试
            print("\n=== 完整响应对象 ===")
            print(f"响应类型: {type(response)}")
            print(f"响应内容: {response}")

            # 转换响应格式，添加更多的错误处理
            try:
                response_dict = {}

                # 检查响应是否有 choices
                if hasattr(response, 'choices') and response.choices:
                    # 检查 choices[0] 是否有 message
                    if hasattr(response.choices[0], 'message'):
                        # 检查 message 是否有 content
                        if hasattr(response.choices[0].message, 'content'):
                            response_dict["content"] = response.choices[0].message.content
                        else:
                            print("警告: 响应中没有 content 字段")
                            response_dict["content"] = ""

                        # 检查是否有工具调用
                        if hasattr(response.choices[0].message, 'tool_calls') and response.choices[0].message.tool_calls:
                            tool_calls = response.choices[0].message.tool_calls
                            if tool_calls and len(tool_calls) > 0:
                                tool_call = tool_calls[0]
                                if hasattr(tool_call, 'function') and tool_call.function:
                                    if hasattr(tool_call.function, 'name') and hasattr(tool_call.function, 'arguments'):
                                        response_dict["function_call"] = {
                                            "name": tool_call.function.name,
                                            "arguments": tool_call.function.arguments
                                        }
                                    else:
                                        print("警告: tool_call.function 没有 name 或 arguments 属性")
                                else:
                                    print("警告: tool_call 没有 function 属性")
                            else:
                                print("警告: tool_calls 为空")

                        # 检查是否有函数调用（ChatGLM 格式）
                        elif hasattr(response.choices[0].message, 'function_call'):
                            function_call = response.choices[0].message.function_call
                            if function_call:
                                if hasattr(function_call, 'name') and hasattr(function_call, 'arguments'):
                                    response_dict["function_call"] = {
                                        "name": function_call.name,
                                        "arguments": function_call.arguments
                                    }
                                else:
                                    print("警告: function_call 没有 name 或 arguments 属性")
                            else:
                                print("警告: function_call 为 None")
                    else:
                        print("警告: choices[0] 没有 message 属性")
                else:
                    print("警告: 响应没有 choices 属性或 choices 为空")
            except Exception as e:
                print(f"处理响应时出错: {str(e)}")
                import traceback
                traceback.print_exc()

            # 打印完整的响应对象，方便调试
            print("\n=== 完整响应对象 ===")
            print(f"响应类型: {type(response)}")
            print(f"响应内容: {response}")

            # 打印响应中的消息内容
            content = response.choices[0].message.content
            print(f"\n=== 消息内容 ===")
            print(f"内容: {content}")

            # 检查是否调用了 eyes_doubao 函数
            if "function_call" in response_dict:
                function_name = response_dict["function_call"]["name"]
                arguments = response_dict["function_call"]["arguments"]
                print(f"\n=== 函数调用信息 (response_dict) ===")
                print(f"函数名: {function_name}")
                print(f"参数: {arguments}")

                if function_name == "eyes_doubao":
                    print("成功: LLM 正确调用了视觉功能!")
                else:
                    print(f"失败: LLM 调用了其他函数: {function_name}")
            else:
                # 检查 OpenAI 响应中是否有工具调用
                if hasattr(response.choices[0].message, 'tool_calls') and response.choices[0].message.tool_calls:
                    tool_calls = response.choices[0].message.tool_calls
                    print(f"\n=== 工具调用信息 (tool_calls) ===")
                    print(f"工具调用数量: {len(tool_calls)}")

                    for i, tool_call in enumerate(tool_calls):
                        print(f"\n工具调用 #{i+1}:")
                        print(f"ID: {tool_call.id}")
                        print(f"类型: {tool_call.type}")
                        print(f"函数名: {tool_call.function.name}")
                        print(f"参数: {tool_call.function.arguments}")

                    # 使用第一个工具调用进行判断
                    function_name = tool_calls[0].function.name
                    print(f"\nLLM 调用了函数: {function_name}")

                    if function_name == "eyes_doubao":
                        print("成功: LLM 正确调用了视觉功能!")

                        # 实际执行 eyes_doubao 函数
                        print("\n=== 执行 eyes_doubao 函数 ===")
                        try:
                            # 调用 eyes_doubao 函数
                            result = eyes_doubao()

                            # 打印函数执行结果
                            print(f"函数执行结果: {result}")

                            # 将函数执行结果返回给 LLM，让 LLM 继续对话
                            print("\n=== 将函数执行结果返回给 LLM ===")

                            # 创建新的消息，包含函数执行结果
                            function_response = {
                                "role": "function",
                                "name": "eyes_doubao",
                                "content": result.response if hasattr(result, 'response') else str(result)
                            }

                            # 将函数执行结果添加到消息历史中
                            messages.append(function_response)

                            # 再次调用 LLM，让 LLM 基于函数执行结果继续对话
                            print("\n=== LLM 基于函数执行结果的回复 ===")
                            second_response = client.chat.completions.create(
                                model=llm_config["model_name"],
                                messages=messages
                            )

                            # 打印 LLM 的回复
                            if hasattr(second_response.choices[0].message, 'content'):
                                print(f"LLM 回复: {second_response.choices[0].message.content}")
                            else:
                                print("LLM 没有回复内容")

                        except Exception as e:
                            print(f"执行 eyes_doubao 函数时出错: {str(e)}")
                            import traceback
                            traceback.print_exc()
                    else:
                        print(f"失败: LLM 调用了其他函数: {function_name}")
                else:
                    print(f"\n=== 直接回复 ===")
                    print(f"LLM 没有调用任何函数，而是直接回复:")
                    print(f"{content}")

    except Exception as e:
        logger.bind(tag=TAG).error(f"测试 LLM 调用视觉意图时出错: {str(e)}")
        print(f"测试 LLM 调用视觉意图时出错: {str(e)}")

if __name__ == "__main__":
    print("运行 LLM 视觉意图测试...")

    print("\n=== 测试 LLM 调用视觉意图 ===")
    test_llm_function_call()

    # 如果需要运行其他测试，请取消下面的注释
    # print("\n=== 测试 1: 直接测试 eyes_doubao 函数（不使用模拟）===")
    # test_direct()
    #
    # print("\n=== 测试 2: 使用模拟数据测试 eyes_doubao 函数 ===")
    # test_with_mock()
