#!/usr/bin/env python3
"""
测试视觉HTTP服务器的图片上传功能
模拟ESP32摄像头上传图片
"""

import asyncio
import aiohttp
import base64
import os
import sys

# 创建一个简单的测试图片（1x1像素的白色PNG）
TEST_IMAGE_BASE64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="

async def test_vision_upload():
    """测试图片上传到视觉服务器"""
    try:
        # 解码测试图片
        image_data = base64.b64decode(TEST_IMAGE_BASE64)
        
        # 创建multipart表单数据
        data = aiohttp.FormData()
        data.add_field('question', '请描述这张图片')
        data.add_field('file', image_data, filename='test.png', content_type='image/png')
        
        # 设置请求头
        headers = {
            'Device-ID': 'test_device_001',
            'Session-ID': 'test_session_001'
        }
        
        # 发送请求
        url = "http://localhost:8003/xiaozhi/vision/explain"
        
        print(f"🚀 正在测试视觉服务器: {url}")
        print(f"📷 上传测试图片，大小: {len(image_data)} 字节")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, data=data, headers=headers) as response:
                print(f"📡 响应状态: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 请求成功!")
                    print(f"📝 响应内容: {result}")
                    
                    if result.get('success'):
                        print(f"🎉 视觉识别成功: {result.get('result', '无结果')}")
                    else:
                        print(f"❌ 视觉识别失败: {result.get('message', '未知错误')}")
                else:
                    error_text = await response.text()
                    print(f"❌ 请求失败: {error_text}")
                    
    except aiohttp.ClientConnectorError:
        print("❌ 连接失败: 视觉HTTP服务器未启动或地址错误")
        print("💡 请先运行: python start_vision_server.py")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def test_health_check():
    """测试健康检查接口"""
    try:
        url = "http://localhost:8003/xiaozhi/vision/health"
        print(f"🔍 测试健康检查: {url}")
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 健康检查通过: {result}")
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    
    except aiohttp.ClientConnectorError:
        print("❌ 健康检查失败: 服务器未启动")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 豆包视觉HTTP服务器测试")
    print("=" * 60)
    
    # 测试健康检查
    await test_health_check()
    print()
    
    # 测试图片上传
    await test_vision_upload()
    print()
    
    print("=" * 60)
    print("🏁 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
