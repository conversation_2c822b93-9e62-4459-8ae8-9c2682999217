# 豆包视觉配置示例
# 请将此配置添加到您的 config.yaml 文件中

# 视觉识别配置（豆包视觉大模型）
# 优先从API端获取配置，此处为本地备用配置
Vision:
  DoubaoVision:
    # 豆包视觉API配置
    api_key: "你的豆包视觉API密钥"  # 替换为实际的API密钥
    base_url: "https://ark.cn-beijing.volces.com/api/v3"
    model: "ep-20241230140547-8xqzr"  # 替换为你的豆包视觉模型ID
    max_tokens: 1000
    temperature: 0.7

# 使用说明：
# 1. 将上述配置添加到您的 config.yaml 文件中
# 2. 替换 "你的豆包视觉API密钥" 为实际的API密钥
# 3. 替换模型ID为您的实际模型ID
# 4. 重启xiaozhi-server服务

# 注意：
# - 如果启用了API配置获取，系统会优先从管理后台获取配置
# - 本地配置作为备用方案，当API配置不可用时使用
# - 连通性测试会在模块加载时自动进行（如果本地配置可用）
