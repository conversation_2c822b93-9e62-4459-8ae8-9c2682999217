"""
视觉HTTP服务器
接收ESP32摄像头上传的图片并进行豆包视觉处理
"""

import os
import json
import asyncio
import base64
from aiohttp import web, multipart
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

class VisionHttpServer:
    """视觉HTTP服务器"""

    def __init__(self, config):
        self.config = config
        self.port = int(config.get("server", {}).get("vision_port", 8003))
        self.app = None
        self.runner = None
        self.site = None

    async def start(self):
        """启动HTTP服务器"""
        try:
            self.app = web.Application()

            # 添加路由
            self.app.router.add_post('/xiaozhi/vision/explain', self._handle_explain)
            self.app.router.add_options('/xiaozhi/vision/explain', self._handle_options)
            self.app.router.add_get('/xiaozhi/vision/health', self._handle_health)

            # 启动服务器
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            self.site = web.TCPSite(self.runner, '0.0.0.0', self.port)
            await self.site.start()

            logger.bind(tag=TAG).info(f"视觉HTTP服务器已启动，端口: {self.port}")

            # 保持服务运行
            while True:
                await asyncio.sleep(3600)  # 每隔 1 小时检查一次

        except Exception as e:
            logger.bind(tag=TAG).error(f"启动视觉HTTP服务器失败: {e}")

    async def stop(self):
        """停止HTTP服务器"""
        try:
            if self.site:
                await self.site.stop()
            if self.runner:
                await self.runner.cleanup()
            logger.bind(tag=TAG).info("视觉HTTP服务器已停止")
        except Exception as e:
            logger.bind(tag=TAG).error(f"停止视觉HTTP服务器失败: {e}")

    async def _handle_options(self, request):
        """处理OPTIONS请求"""
        response = web.Response()
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "POST, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Device-ID, Session-ID, Authorization"
        return response

    async def _handle_explain(self, request):
        """处理图片解释请求"""
        try:
            # 添加CORS头
            headers = {
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Device-ID, Session-ID, Authorization"
            }

            # 获取设备ID和会话ID
            device_id = request.headers.get('Device-ID', 'unknown')
            session_id = request.headers.get('Session-ID', 'default')

            logger.bind(tag=TAG).info(f"收到图片解释请求，设备ID: {device_id}, 会话ID: {session_id}")

            # 解析multipart/form-data
            reader = multipart.MultipartReader.from_response(request)
            question = "请描述这张图片"
            image_data = None

            async for part in reader:
                if part.name == 'question':
                    question = await part.text()
                elif part.name == 'file':
                    image_data = await part.read()

            if not image_data:
                return web.Response(
                    text=json.dumps({"success": False, "message": "未收到图片数据"}),
                    content_type="application/json",
                    status=400,
                    headers=headers
                )

            # 保存图片
            os.makedirs("tmp", exist_ok=True)
            image_path = f"tmp/vision_{session_id}_{device_id}.jpg"

            with open(image_path, 'wb') as f:
                f.write(image_data)

            logger.bind(tag=TAG).info(f"图片已保存: {image_path}, 大小: {len(image_data)} 字节, 问题: {question}")

            # 处理图片
            result = await self._process_image(image_path, question, device_id, session_id)

            return web.Response(
                text=json.dumps(result),
                content_type="application/json",
                headers=headers
            )

        except Exception as e:
            logger.bind(tag=TAG).error(f"处理图片解释请求失败: {e}")
            return web.Response(
                text=json.dumps({"success": False, "message": f"处理失败: {str(e)}"}),
                content_type="application/json",
                status=500,
                headers=headers
            )

    async def _process_image(self, image_path, question, device_id, session_id):
        """处理图片"""
        try:
            # 调用豆包视觉处理功能
            result_text = await self._call_vision_ai(image_path, question)

            if result_text:
                return {
                    "success": True,
                    "result": result_text,
                    "question": question
                }
            else:
                return {
                    "success": False,
                    "message": "图片处理失败或结果为空"
                }

        except Exception as e:
            logger.bind(tag=TAG).error(f"处理图片时出错: {e}")
            return {
                "success": False,
                "message": f"处理图片时出错: {str(e)}"
            }

    async def _call_vision_ai(self, image_path, question):
        """调用豆包视觉AI处理图片"""
        try:
            # 导入豆包视觉处理函数
            from plugins_func.functions.isee_eyes_doubao import get_doubao_vision_provider

            # 创建临时连接对象
            class TempConnection:
                def __init__(self, config):
                    self.config = config
                    self.device_id = "vision_http_server"
                    self.client_id = "vision_http_server"
                    self.session_id = "vision_http_server"

            temp_conn = TempConnection(self.config)

            # 获取豆包视觉提供者
            vision_provider = get_doubao_vision_provider(temp_conn)

            if vision_provider.initialized:
                # 调用豆包视觉分析
                result = await vision_provider.analyze_image(image_path, question)
                return result
            else:
                logger.bind(tag=TAG).error("豆包视觉提供者未初始化")
                return None

        except Exception as e:
            logger.bind(tag=TAG).error(f"调用豆包视觉AI失败: {e}")
            return None

    async def _handle_health(self, request):
        """健康检查"""
        return web.Response(
            text=json.dumps({"status": "ok", "message": "视觉HTTP服务器运行正常"}),
            content_type="application/json",
            headers={
                "Access-Control-Allow-Origin": "*"
            }
        )


# 全局服务器实例
vision_http_server = None

async def start_vision_http_server(config):
    """启动视觉HTTP服务器"""
    global vision_http_server

    if vision_http_server is None:
        vision_http_server = VisionHttpServer(config)
        # 在后台启动服务器
        asyncio.create_task(vision_http_server.start())
        logger.bind(tag=TAG).info("视觉HTTP服务器启动任务已创建")
    else:
        logger.bind(tag=TAG).warning("视觉HTTP服务器已经在运行")

async def stop_vision_http_server():
    """停止视觉HTTP服务器"""
    global vision_http_server

    if vision_http_server:
        await vision_http_server.stop()
        vision_http_server = None
        logger.bind(tag=TAG).info("视觉HTTP服务器已停止")
    else:
        logger.bind(tag=TAG).warning("视觉HTTP服务器未运行")


if __name__ == "__main__":
    # 测试启动
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    from config.config_loader import load_config

    async def test():
        config = load_config()
        await start_vision_http_server(config)

    asyncio.run(test())
