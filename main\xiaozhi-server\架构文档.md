# 小智服务器架构文档

## 1. 项目概述

小智服务器是一个基于Python的智能语音助手后端系统，提供语音识别、语音合成、自然语言处理和意图识别等功能。系统采用模块化设计，支持多种AI服务提供商，并提供插件机制以扩展功能。

### 1.1 核心功能

- **语音识别(ASR)**: 将用户语音转换为文本
- **语音合成(TTS)**: 将文本转换为语音
- **自然语言处理(LLM)**: 理解用户意图并生成回复
- **意图识别**: 分析用户请求并执行相应功能
- **WebSocket通信**: 与客户端设备实时通信
- **OTA更新**: 提供设备固件更新功能
- **插件系统**: 支持功能扩展
- **视觉功能**: 通过设备摄像头分析图像内容

## 2. 系统架构

### 2.1 整体架构

小智服务器采用分层架构设计，主要包括以下几层：

1. **通信层**: 负责与客户端的WebSocket通信
2. **处理层**: 负责处理各类消息和请求
3. **AI服务层**: 提供ASR、TTS、LLM等AI能力
4. **插件层**: 提供功能扩展机制
5. **配置层**: 管理系统配置

### 2.2 目录结构

```
xiaozhi-server/
├── app.py                  # 主程序入口
├── config/                 # 配置相关
│   ├── config_loader.py    # 配置加载器
│   ├── logger.py           # 日志配置
│   ├── manage_api_client.py # 管理API客户端
│   └── settings.py         # 设置管理
├── core/                   # 核心功能
│   ├── auth.py             # 认证模块
│   ├── connection.py       # 连接处理
│   ├── handle/             # 消息处理器
│   │   ├── abortHandle.py  # 中断处理
│   │   ├── functionHandler.py # 功能处理
│   │   ├── helloHandle.py  # 欢迎处理
│   │   ├── intentHandler.py # 意图处理
│   │   ├── iotHandle.py    # IoT设备处理
│   │   ├── receiveAudioHandle.py # 音频接收处理
│   │   ├── sendAudioHandle.py # 音频发送处理
│   │   ├── textHandle.py   # 文本处理
│   │   └── ttsReportHandle.py # TTS报告处理
│   ├── mcp/                # MCP管理
│   │   ├── manager.py      # MCP管理器
│   │   └── MCPClient.py    # MCP客户端
│   ├── ota_server.py       # OTA更新服务器
│   ├── providers/          # 服务提供商
│   │   ├── asr/            # 语音识别提供商
│   │   ├── intent/         # 意图识别提供商
│   │   ├── llm/            # 大语言模型提供商
│   │   ├── memory/         # 记忆提供商
│   │   ├── tts/            # 语音合成提供商
│   │   └── vad/            # 语音活动检测提供商
│   ├── utils/              # 工具类
│   │   ├── asr.py          # ASR工具
│   │   ├── dialogue.py     # 对话管理
│   │   ├── intent.py       # 意图工具
│   │   ├── llm.py          # LLM工具
│   │   ├── memory.py       # 记忆工具
│   │   ├── output_counter.py # 输出计数器
│   │   ├── p3.py           # P3工具
│   │   ├── tts.py          # TTS工具
│   │   ├── util.py         # 通用工具
│   │   └── vad.py          # VAD工具
│   └── websocket_server.py # WebSocket服务器
├── models/                 # 模型文件
├── plugins_func/           # 插件功能
│   ├── functions/          # 功能实现
│   │   ├── change_role.py  # 角色切换
│   │   ├── eyes_doubao.py  # 视觉功能
│   │   ├── get_news.py     # 新闻获取
│   │   ├── get_time.py     # 时间查询
│   │   ├── get_weather.py  # 天气查询
│   │   ├── handle_device.py # 设备控制
│   │   ├── play_music.py   # 音乐播放
│   │   ├── vision_analyze.py # 高级视觉分析
│   │   └── ...             # 其他功能插件
│   ├── loadplugins.py      # 插件加载
│   └── register.py         # 插件注册
└── performance_tester.py   # 性能测试
```

## 3. 核心模块详解

### 3.1 WebSocket服务器 (core/websocket_server.py)

WebSocket服务器是系统的核心组件之一，负责：
- 创建和管理WebSocket服务器
- 初始化核心AI组件(VAD,ASR,LLM等)
- 处理设备连接请求
- 维护活跃连接池

### 3.2 连接处理器 (core/connection.py)

连接处理器负责管理单个设备的WebSocket连接生命周期，包括：
- 连接认证
- 消息处理
- 语音识别和合成
- 对话管理
- 插件功能调用

### 3.3 消息处理模块 (core/handle/)

消息处理模块包含多个处理器，负责处理不同类型的消息：
- **receiveAudioHandle.py**: 处理接收到的音频数据
- **sendAudioHandle.py**: 处理发送音频数据
- **textHandle.py**: 处理文本消息
- **intentHandler.py**: 处理用户意图
- **functionHandler.py**: 处理功能调用

### 3.4 AI服务提供商 (core/providers/)

系统支持多种AI服务提供商，每种服务都有统一的接口和多种实现：
- **ASR提供商**: 阿里云、百度、腾讯等
- **TTS提供商**: 阿里云、百度、OpenAI等
- **LLM提供商**: OpenAI、Gemini、Ollama等
- **意图识别提供商**: 基于LLM的意图识别等
- **记忆提供商**: 本地短期记忆、mem0ai等

### 3.5 插件系统 (plugins_func/)

插件系统允许开发者扩展系统功能，主要包括：
- **插件注册机制**: 通过装饰器注册功能
- **功能处理器**: 执行插件功能
- **内置插件**: 时间查询、天气查询、设备控制等

### 3.6 视觉功能模块 (plugins_func/functions/)

视觉功能模块允许小智通过设备摄像头看到并分析周围环境：
- **eyes_doubao.py**: 基本视觉功能，使用OpenAI视觉模型分析图像
- **vision_analyze.py**: 高级视觉分析功能，支持用户自定义分析提示

## 4. 通信协议

### 4.1 WebSocket消息类型

系统通过WebSocket与客户端通信，支持以下消息类型：

| 消息类型 | 描述 | 方向 |
|---------|------|------|
| text | 文本消息 | 双向 |
| audio | 音频数据 | 客户端→服务器 |
| send_audio | 发送音频请求 | 客户端→服务器 |
| stt | 语音识别结果 | 服务器→客户端 |
| tts | TTS状态消息 | 服务器→客户端 |
| llm | LLM响应消息 | 服务器→客户端 |
| config_update | 配置更新 | 双向 |

### 4.2 音频处理流程

1. 客户端发送音频数据到服务器
2. 服务器进行VAD检测判断是否有语音
3. 当检测到语音结束时，进行ASR识别
4. 识别结果发送给客户端并进行意图分析
5. 根据意图调用相应功能或LLM生成回复
6. 将回复文本通过TTS转换为语音
7. 将语音数据发送给客户端播放

### 4.3 视觉处理流程

1. 用户通过语音命令或LLM函数调用激活视觉功能
2. 服务器从客户端获取图像数据（通过HTTP请求）
3. 服务器处理图像（镜像翻转等）
4. 服务器将图像发送给OpenAI视觉模型进行分析
5. 分析结果返回给LLM进行处理
6. LLM生成回复并通过TTS转换为语音
7. 将语音数据发送给客户端播放

## 5. 配置系统

### 5.1 配置文件结构

系统使用YAML格式的配置文件，支持默认配置和自定义配置的合并：
- **config.yaml**: 默认配置文件
- **data/.config.yaml**: 用户自定义配置文件

### 5.2 主要配置项

- **server**: 服务器配置（IP、端口等）
- **selected_module**: 选择使用的模块（ASR、TTS、LLM等）
- **ASR/TTS/LLM**: 各AI服务提供商的配置
- **Intent**: 意图识别配置
- **log**: 日志配置
- **device**: 设备配置（IP等）

### 5.3 配置加载流程

1. 加载默认配置文件
2. 加载用户自定义配置文件
3. 合并配置（用户配置优先）
4. 如果配置了API，则从API获取配置
5. 确保所有必要的目录存在

## 6. 插件开发指南

### 6.1 插件结构

插件应放置在`plugins_func/functions/`目录下，每个插件包含：
- 功能实现函数
- 功能描述（用于LLM调用）
- 功能类型定义

### 6.2 插件注册

使用`@register_function`装饰器注册插件：

```python
from plugins_func.register import register_function, Action, ActionResponse, ToolType

@register_function(
    name="get_time",
    desc={
        "function": {
            "name": "get_time",
            "description": "获取当前时间",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    },
    type=ToolType.WAIT
)
def get_time():
    # 实现功能
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return ActionResponse(
        action=Action.REQLLM,
        result=f"当前时间是 {current_time}",
        response=""
    )
```

### 6.3 插件类型

系统支持多种类型的插件：
- **NONE**: 调用完工具后，不做其他操作
- **WAIT**: 调用工具，等待函数返回
- **CHANGE_SYS_PROMPT**: 修改系统提示词，切换角色性格或职责
- **SYSTEM_CTL**: 系统控制，影响正常的对话流程
- **IOT_CTL**: IOT设备控制
- **MCP_CLIENT**: MCP客户端

### 6.4 视觉功能插件开发

开发视觉功能插件需要注意以下几点：

1. 获取客户端IP地址：
```python
def get_client_ip(conn=None):
    try:
        if conn and hasattr(conn, 'client_ip'):
            return conn.client_ip
        
        # 如果无法从连接获取IP，尝试从配置获取
        config = get_config()
        device_ip = config.get("device", {}).get("ip")
        if device_ip:
            return device_ip
    except Exception as e:
        logger.error(f"获取客户端IP失败: {e}")
    
    # 如果都失败，返回默认IP
    return "***********"  # ESP32默认IP
```

2. 从设备获取图像：
```python
url = f'http://{client_ip}/jpg'
response = requests.get(url, timeout=5)
```

3. 处理图像（镜像翻转等）：
```python
img = cv2.imread('tmp/image.jpg')
flipped_img = cv2.flip(img, 1)
cv2.imwrite('tmp/flipped_image.jpg', flipped_img)
```

4. 调用OpenAI视觉模型：
```python
client = OpenAI(api_key=api_key, base_url=base_url)
messages = [
    {
        "role": "user",
        "content": [
            {"type": "text", "text": prompt},
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{base64_image}"
                }
            }
        ]
    }
]
response = client.chat.completions.create(
    model="gpt-4-vision-preview",
    messages=messages,
    max_tokens=1000
)
```

## 7. 功能完成进度

### 7.1 已完成功能

- [x] WebSocket服务器
- [x] 语音活动检测(VAD)
- [x] 语音识别(ASR)
- [x] 语音合成(TTS)
- [x] 大语言模型集成(LLM)
- [x] 意图识别
- [x] 基本插件系统
- [x] OTA更新服务器
- [x] 配置管理
- [x] 多种AI服务提供商支持
- [x] 视觉功能（基于OpenAI视觉模型）

### 7.2 进行中功能

- [ ] 更多语音模型支持
- [ ] 多轮对话优化
- [ ] 更丰富的插件生态
- [ ] 性能优化
- [ ] 视觉功能增强（物体检测、OCR等）

### 7.3 计划功能

- [ ] 多设备协同
- [ ] 离线语音识别增强
- [ ] 用户个性化配置
- [ ] Web管理界面
- [ ] 本地视觉模型支持

## 8. 二次开发指南

### 8.1 环境准备

1. 安装Python 3.8+
2. 安装依赖：`pip install -r requirements.txt`
3. 确保安装了FFmpeg（音频处理必需）

### 8.2 添加新的AI服务提供商

1. 在对应的providers目录下创建新文件（如`core/providers/asr/new_provider.py`）
2. 继承基类并实现必要方法
3. 在配置文件中添加新提供商的配置

示例（添加新的ASR提供商）：
```python
from core.providers.asr.base import ASRBase

class NewASRProvider(ASRBase):
    def __init__(self, config):
        super().__init__(config)
        # 初始化代码
        
    def recognize(self, audio_data):
        # 实现语音识别
        return recognized_text
```

### 8.3 添加新的插件功能

1. 在`plugins_func/functions/`目录下创建新文件
2. 使用`@register_function`装饰器注册功能
3. 实现功能逻辑并返回适当的`ActionResponse`

### 8.4 修改系统行为

1. 了解相关模块的代码和工作流程
2. 修改对应的处理器或工具类
3. 确保修改不会破坏现有功能
4. 添加适当的日志和错误处理

## 9. 常见问题与解决方案

### 9.1 配置问题

- **问题**: 找不到配置文件
- **解决方案**: 确保`data/.config.yaml`文件存在，可以从`config.yaml`复制并修改

### 9.2 音频处理问题

- **问题**: 无法处理音频
- **解决方案**: 确保FFmpeg已正确安装，并可在命令行中访问

### 9.3 AI服务连接问题

- **问题**: 无法连接到AI服务
- **解决方案**: 检查API密钥和网络连接，确保配置正确

### 9.4 插件加载问题

- **问题**: 插件未被加载
- **解决方案**: 检查插件注册是否正确，确保插件名称在配置中启用

### 9.5 视觉功能问题

- **问题**: 无法获取设备图像
- **解决方案**: 检查设备IP地址是否正确，确保设备摄像头正常工作

- **问题**: 视觉模型分析失败
- **解决方案**: 检查OpenAI API密钥和网络连接，确保使用支持视觉的模型

## 10. 贡献指南

我们欢迎社区贡献，包括但不限于：
- 修复bug
- 添加新功能
- 改进文档
- 优化性能

贡献前请先了解项目架构和代码规范，确保提交的代码符合项目要求。

---

本文档旨在帮助开发者快速了解小智服务器的架构和功能，进行二次开发。如有问题或建议，请提交Issue或联系项目维护者。
