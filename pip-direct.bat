@echo off
echo 直接运行 pip...

REM 设置 Python 可执行文件路径
set "PYTHON_EXE=D:\Program Files\anaconda\envs\xiaozhi-esp32-server\python.exe"

REM 检查 Python 可执行文件是否存在
if not exist "%PYTHON_EXE%" (
    echo 错误: 找不到 Python 可执行文件: %PYTHON_EXE%
    echo 尝试使用基础环境的 Python...
    set "PYTHON_EXE=D:\Program Files\anaconda\python.exe"
    
    if not exist "%PYTHON_EXE%" (
        echo 错误: 找不到 Python 可执行文件: %PYTHON_EXE%
        echo 请检查 Anaconda 安装。
        pause
        exit /b 1
    )
)

REM 运行 pip
echo 使用 Python: %PYTHON_EXE%
"%PYTHON_EXE%" -m pip %*

pause
